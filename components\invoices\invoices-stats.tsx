'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { TrendingUp, Clock, CheckCircle, Package, DollarSign } from 'lucide-react'

interface InvoicesStatsProps {
  totalPaid: number
  totalPending: number
  totalInvoices: number
  totalDelivered: number
}

export function InvoicesStats({ totalPaid, totalPending, totalInvoices, totalDelivered }: InvoicesStatsProps) {
  const { language } = useLanguage()

  const stats = [
    {
      title: getTranslation(language, 'totalPaid'),
      value: `₹${totalPaid.toLocaleString()}`,
      icon: CheckCircle,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      borderColor: 'border-emerald-200',
      gradient: 'success-gradient',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      title: getTranslation(language, 'totalPending'),
      value: `₹${totalPending.toLocaleString()}`,
      icon: Clock,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200',
      gradient: 'warning-gradient',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: getTranslation(language, 'totalDelivered'),
      value: totalDelivered,
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      gradient: 'action-gradient',
      change: '+8%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Invoices',
      value: totalInvoices,
      icon: TrendingUp,
      color: 'text-slate-600',
      bgColor: 'bg-slate-50',
      borderColor: 'border-slate-200',
      gradient: 'brand-gradient',
      change: '+15%',
      changeType: 'positive' as const,
    },
  ]

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index} className="professional-card card-hover border-0 shadow-professional bg-white/90 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-semibold text-slate-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2.5 rounded-xl ${stat.bgColor} ${stat.borderColor} border`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="amount-display text-2xl font-bold text-slate-900 mb-1">
                {stat.value}
              </div>
              <div className="flex items-center justify-between">
                <div className={`h-1 flex-1 rounded-full ${stat.gradient} mr-3`}></div>
                <span className={`text-xs font-medium ${
                  stat.changeType === 'positive' ? 'text-emerald-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </span>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}