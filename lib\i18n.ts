export const translations = {
  en: {
    // Navigation
    dashboard: 'Dashboard',
    logout: 'Logout',
    invoices: 'Invoices',
    
    // Authentication
    login: 'Login',
    loginWithEmail: 'Login with <PERSON>ail',
    enterEmail: 'Enter your email address',
    emailAddress: 'Email Address',
    sendMagicLink: 'Send Magic Link',
    magicLinkSent: 'Magic link sent to your email',
    checkEmail: 'Check your email',
    emailSent: 'Email Sent!',
    checkEmailInbox: 'We sent you a login link. Check your email inbox.',
    clickLinkToLogin: 'Click the link in your email to login securely.',
    checkSpamFolder: 'Don\'t see it? Check your spam folder.',
    sendAnotherLink: 'Send another link',
    magicLinkDescription: 'We\'ll send you a secure login link via email',
    
    // Language
    language: 'Language',
    english: 'English',
    bengali: 'বাংলা',
    
    // Dashboard sections
    paid: 'Paid',
    pending: 'Pending',
    delivered: 'Delivered',
    
    // Invoice details
    client: 'Client',
    service: 'Service',
    amount: 'Amount',
    date: 'Date',
    dueDate: 'Due Date',
    paymentDate: 'Payment Date',
    deliveryDate: 'Delivery Date',
    notes: 'Notes',
    
    // Actions
    markAsPaid: 'Mark as Paid',
    markAsDelivered: 'Mark as Delivered',
    generateQr: 'Generate QR Code',
    payNow: 'Pay Now',
    addInvoice: 'Add Invoice',
    help: 'Help',
    
    // UPI
    upiPayment: 'UPI Payment',
    scanQrCode: 'Scan QR Code to Pay',
    payTo: 'Pay to',
    
    // Status
    totalPaid: 'Total Paid',
    totalPending: 'Total Pending',
    totalDelivered: 'Total Delivered',
    
    // Profile
    profile: 'Profile',
    businessName: 'Business Name',
    name: 'Name',
    upiId: 'UPI ID',
    saveProfile: 'Save Profile',
    
    // Messages
    success: 'Success',
    error: 'Error',
    loading: 'Loading...',
    noInvoices: 'No invoices found',
    offlineMode: 'Offline Mode',
    syncWhenOnline: 'Changes will sync when online',
  },
  bn: {
    // Navigation
    dashboard: 'ড্যাশবোর্ড',
    logout: 'লগআউট',
    invoices: 'ইনভয়েস',
    
    // Authentication
    login: 'লগইন',
    loginWithEmail: 'ইমেইল দিয়ে লগইন করুন',
    enterEmail: 'আপনার ইমেইল ঠিকানা লিখুন',
    emailAddress: 'ইমেইল ঠিকানা',
    sendMagicLink: 'ম্যাজিক লিঙ্ক পাঠান',
    magicLinkSent: 'আপনার ইমেইলে ম্যাজিক লিঙ্ক পাঠানো হয়েছে',
    checkEmail: 'আপনার ইমেইল চেক করুন',
    emailSent: 'ইমেইল পাঠানো হয়েছে!',
    checkEmailInbox: 'আমরা আপনাকে একটি লগইন লিঙ্ক পাঠিয়েছি। আপনার ইমেইল ইনবক্স চেক করুন।',
    clickLinkToLogin: 'নিরাপদে লগইন করতে আপনার ইমেইলের লিঙ্কে ক্লিক করুন।',
    checkSpamFolder: 'দেখতে পাচ্ছেন না? আপনার স্প্যাম ফোল্ডার চেক করুন।',
    sendAnotherLink: 'আরেকটি লিঙ্ক পাঠান',
    magicLinkDescription: 'আমরা আপনাকে ইমেইলের মাধ্যমে একটি নিরাপদ লগইন লিঙ্ক পাঠাবো',
    
    // Language
    language: 'ভাষা',
    english: 'English',
    bengali: 'বাংলা',
    
    // Dashboard sections
    paid: 'পরিশোধিত',
    pending: 'বকেয়া',
    delivered: 'সরবরাহকৃত',
    
    // Invoice details
    client: 'ক্লায়েন্ট',
    service: 'সেবা',
    amount: 'পরিমাণ',
    date: 'তারিখ',
    dueDate: 'পরিশোধের তারিখ',
    paymentDate: 'পেমেন্টের তারিখ',
    deliveryDate: 'ডেলিভারির তারিখ',
    notes: 'নোট',
    
    // Actions
    markAsPaid: 'পরিশোধিত হিসাবে চিহ্নিত করুন',
    markAsDelivered: 'সরবরাহিত হিসাবে চিহ্নিত করুন',
    generateQr: 'QR কোড তৈরি করুন',
    payNow: 'এখনই পেমেন্ট করুন',
    addInvoice: 'ইনভয়েস যোগ করুন',
    help: 'সহায়তা',
    
    // UPI
    upiPayment: 'UPI পেমেন্ট',
    scanQrCode: 'পেমেন্টের জন্য QR কোড স্ক্যান করুন',
    payTo: 'পেমেন্ট করুন',
    
    // Status
    totalPaid: 'মোট পরিশোধিত',
    totalPending: 'মোট বকেয়া',
    totalDelivered: 'মোট সরবরাহকৃত',
    
    // Profile
    profile: 'প্রোফাইল',
    businessName: 'ব্যবসার নাম',
    name: 'নাম',
    upiId: 'UPI আইডি',
    saveProfile: 'প্রোফাইল সংরক্ষণ করুন',
    
    // Messages
    success: 'সফল',
    error: 'ত্রুটি',
    loading: 'লোড হচ্ছে...',
    noInvoices: 'কোন ইনভয়েস পাওয়া যায়নি',
    offlineMode: 'অফলাইন মোড',
    syncWhenOnline: 'অনলাইনে এলে পরিবর্তনগুলি সিঙ্ক হবে',
  }
} as const

export type Language = keyof typeof translations
export type TranslationKey = keyof typeof translations.en

export function getTranslation(lang: Language, key: TranslationKey): string {
  return translations[lang][key] || translations.en[key]
}