'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useLanguage } from '@/contexts/language-context'
import { useAuth } from '@/contexts/auth-context'
import { getTranslation } from '@/lib/i18n'
import { ArrowLeft, FileText, Download, Filter } from 'lucide-react'
import { useRouter } from 'next/navigation'

export function InvoicesHeader() {
  const { language } = useLanguage()
  const { signOut } = useAuth()
  const router = useRouter()

  return (
    <header className="bg-white/95 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-50 shadow-sm">
      <div className="clean-layout">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/')}
              className="hover:bg-slate-100 p-2"
              aria-label="Go back to dashboard"
            >
              <ArrowLeft className="h-5 w-5 text-slate-600" />
            </Button>
            
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl action-gradient flex items-center justify-center">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900">
                  {getTranslation(language, 'invoices') || 'Invoices'}
                </h1>
                <p className="text-xs text-slate-500 -mt-1 font-medium">
                  Manage your invoices
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="border-slate-200 hover:border-blue-500/50 transition-colors hidden sm:flex"
              aria-label="Export invoices"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={signOut}
              className="border-slate-200 hover:border-red-300 hover:bg-red-50 hover:text-red-600 transition-colors"
            >
              {getTranslation(language, 'logout')}
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
}