'use client'

import { Suspense } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { PhoneAuth } from '@/components/auth/phone-auth'
import { CustomersContent } from '@/components/customers/customers-content'
import ErrorBoundary from '@/components/error-boundary'
import { Logo } from '@/components/ui/logo' // Assuming you have an ErrorBoundary component

export default function CustomersPage() {
  const { user, loading: authLoading } = useAuth()

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="text-center">
          <div className="relative mb-4">
            <Logo size="md" variant="icon-only" />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
            </div>
          </div>
          <p className="text-slate-600 font-medium">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return <PhoneAuth />
  }

  return (
    <ErrorBoundary>
      <Suspense fallback={<div>Loading customers...</div>}>
        <CustomersContent />
      </Suspense>
    </ErrorBoundary>
  )
}
