'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Plus, FileText, Users, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { useRouter } from 'next/navigation'

interface FabSpeedDialProps {
  onAddInvoice: () => void
  onAddCustomer?: () => void
  className?: string
}

export function FabSpeedDial({ onAddInvoice, onAddCustomer, className }: FabSpeedDialProps) {
  const { language } = useLanguage()
  const router = useRouter()
  const [isExpanded, setIsExpanded] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const fabRef = useRef<HTMLDivElement>(null)

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (fabRef.current && !fabRef.current.contains(event.target as Node)) {
        if (isExpanded) {
          setIsExpanded(false)
        }
      }
    }

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('touchstart', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('touchstart', handleClickOutside)
    }
  }, [isExpanded])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isExpanded) {
        setIsExpanded(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isExpanded])

  const toggleExpanded = () => {
    if (isAnimating) return
    
    setIsAnimating(true)
    setIsExpanded(!isExpanded)
    
    // Haptic feedback (if supported)
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }
    
    setTimeout(() => setIsAnimating(false), 300)
  }

  const handleAddInvoice = () => {
    onAddInvoice()
    setIsExpanded(false)
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(30)
    }
  }

  const handleAddCustomer = () => {
    if (onAddCustomer) {
      onAddCustomer()
    } else {
      // Navigate to add customer page
      router.push('/customers/new')
    }
    setIsExpanded(false)
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(30)
    }
  }

  const speedDialItems = [
    {
      id: 'add-customer',
      label: 'Add New Customer',
      icon: Users,
      onClick: handleAddCustomer,
      color: 'bg-emerald-500 hover:bg-emerald-600',
      delay: 0,
    },
    {
      id: 'create-invoice',
      label: getTranslation(language, 'addInvoice'),
      icon: FileText,
      onClick: handleAddInvoice,
      color: 'action-gradient hover:opacity-90',
      delay: 50,
    },
  ]

  return (
    <>
      {/* Backdrop */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-[2px] z-40 transition-all duration-300 ease-out"
          onClick={() => setIsExpanded(false)}
          aria-hidden="true"
        />
      )}

      {/* FAB Container */}
      <div 
        ref={fabRef}
        className={cn(
          "fixed bottom-20 right-6 z-50 flex flex-col items-end",
          className
        )}
        role="menu"
        aria-label="Quick actions menu"
        aria-expanded={isExpanded}
      >
        {/* Speed Dial Items */}
        <div 
          className={cn(
            "flex flex-col items-end space-y-4 mb-4 transition-all duration-300 ease-out",
            isExpanded 
              ? "opacity-100 translate-y-0 pointer-events-auto scale-100" 
              : "opacity-0 translate-y-8 pointer-events-none scale-95"
          )}
        >
          {speedDialItems.map((item, index) => {
            const Icon = item.icon
            return (
              <div
                key={item.id}
                className={cn(
                  "flex items-center space-x-3 transition-all duration-300 ease-out",
                  isExpanded 
                    ? "opacity-100 translate-x-0" 
                    : "opacity-0 translate-x-4"
                )}
                style={{
                  transitionDelay: isExpanded ? `${item.delay}ms` : '0ms'
                }}
              >
                {/* Label */}
                <div 
                  className={cn(
                    "bg-white/95 backdrop-blur-sm px-4 py-2 rounded-xl shadow-lg border border-slate-200",
                    "transition-all duration-200 ease-out",
                    "whitespace-nowrap",
                    isExpanded ? "opacity-100 scale-100" : "opacity-0 scale-95"
                  )}
                  style={{
                    transitionDelay: isExpanded ? `${item.delay + 100}ms` : '0ms'
                  }}
                >
                  <span className="text-sm font-semibold text-slate-700">
                    {item.label}
                  </span>
                </div>

                {/* Action Button */}
                <Button
                  onClick={item.onClick}
                  className={cn(
                    "w-14 h-14 rounded-full shadow-xl",
                    item.color,
                    "text-white flex items-center justify-center",
                    "transition-all duration-200 ease-out",
                    "hover:scale-110 active:scale-95",
                    "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                    "touch-target"
                  )}
                  size="sm"
                  role="menuitem"
                  aria-label={item.label}
                  tabIndex={isExpanded ? 0 : -1}
                >
                  <Icon className="h-6 w-6" aria-hidden="true" />
                </Button>
              </div>
            )
          })}
        </div>

        {/* Main FAB Button */}
        <Button
          onClick={toggleExpanded}
          disabled={isAnimating}
          className={cn(
            "w-16 h-16 rounded-full shadow-2xl",
            "brand-gradient hover:opacity-90 text-white",
            "flex items-center justify-center",
            "transition-all duration-300 ease-out",
            "hover:scale-110 active:scale-95",
            "focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2",
            "touch-target relative overflow-hidden",
            "disabled:opacity-70 disabled:cursor-not-allowed",
            isExpanded && "rotate-45 scale-110"
          )}
          aria-label={isExpanded ? "Close quick actions menu" : "Open quick actions menu"}
          aria-expanded={isExpanded}
          aria-haspopup="menu"
          role="button"
        >
          {/* Icon with smooth transition */}
          <div className="relative w-7 h-7 flex items-center justify-center">
            <Plus 
              className={cn(
                "absolute transition-all duration-300 ease-out",
                isExpanded 
                  ? "rotate-45 scale-110 opacity-100" 
                  : "rotate-0 scale-100 opacity-100"
              )} 
              size={28}
              aria-hidden="true" 
            />
          </div>

          {/* Ripple effect */}
          <div 
            className={cn(
              "absolute inset-0 rounded-full",
              "bg-white/20 scale-0 transition-transform duration-300 ease-out",
              isExpanded && "scale-100"
            )}
          />
        </Button>

        {/* Screen reader instructions */}
        <div className="sr-only" aria-live="polite">
          {isExpanded 
            ? "Quick actions menu is open. Press Escape to close or select an action." 
            : "Quick actions menu is closed. Press to open menu."
          }
        </div>
      </div>
    </>
  )
}