# Logo Component Usage Examples

The Logo component provides a flexible and reusable way to display the InstraBillBoi brand across the application.

## Basic Usage

```tsx
import { Logo, AuthLogo } from '@/components/ui/logo'

// Default logo (medium size, full variant)
<Logo />

// Small logo with tagline
<Logo size="sm" showTagline={true} />

// Large logo, icon only
<Logo size="lg" variant="icon-only" />

// Text only variant
<Logo variant="text-only" />

// Clickable logo
<Logo onClick={() => router.push('/dashboard')} />

// Authentication page logo
<AuthLogo />
```

## Props

### Logo Component

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Size of the logo |
| `variant` | `'full' \| 'icon-only' \| 'text-only'` | `'full'` | Display variant |
| `className` | `string` | - | Additional CSS classes |
| `showTagline` | `boolean` | `false` | Show tagline below company name |
| `onClick` | `() => void` | - | Click handler (makes logo clickable) |

### AuthLogo Component

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `className` | `string` | - | Additional CSS classes |

## Size Configurations

- **sm**: 40px container, suitable for headers and compact spaces
- **md**: 60px container, default size for most use cases
- **lg**: 80px container, good for prominent placement and hero sections

## Variants

- **full**: Shows both icon and text (default)
- **icon-only**: Shows only the building icon
- **text-only**: Shows only the company name and optional tagline

## Current Usage in Codebase

1. **Login Page**: Uses `AuthLogo` via `AuthBranding` component
2. **Dashboard Header**: Uses `Logo` with `size="sm"` and `showTagline={true}`
3. **Loading States**: Could use `Logo` with `variant="icon-only"`

## Accessibility Features

- Proper ARIA labels for screen readers
- Semantic HTML structure
- Focus management for clickable variants
- High contrast colors for visibility

## Styling

The logo uses the `brand-gradient` CSS class for consistent branding. The gradient and colors are defined in the global CSS file.
