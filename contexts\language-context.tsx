'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { Language } from '@/lib/i18n'
import { useAuth } from './auth-context'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const { user, loading: authLoading, profile } = useAuth()
  const [language, setLanguage] = useState<Language>('en')
  const [initialLoad, setInitialLoad] = useState(true)

  useEffect(() => {
    if (!authLoading && initialLoad) {
      if (user && profile?.language) {
        // If user is logged in and has a language in profile
        setLanguage(profile.language)
      } else {
        // Fallback to localStorage for guests or if profile language is not set
        const savedLanguage = localStorage.getItem('language') as Language
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'bn')) {
          setLanguage(savedLanguage)
        }
      }
      setInitialLoad(false)
    }
  }, [user, authLoading, initialLoad, profile])

  const handleSetLanguage = async (lang: Language) => {
    setLanguage(lang)
    localStorage.setItem('language', lang)

    if (user) {
      // If user is logged in, update their profile in the database
      try {
        await fetch('/api/user/language', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ language: lang }),
        })
      } catch (error) {
        console.error('Failed to update user language in DB:', error)
      }
    }
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}