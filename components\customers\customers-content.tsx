'use client'

import { useState } from 'react'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { useCustomers } from '@/hooks/use-customers'
import { CustomersHeader } from '@/components/customers/customers-header'
import { CustomersFilters } from '@/components/customers/customers-filters'
import { CustomersGrid } from '@/components/customers/customers-grid'
import { CustomersStats } from '@/components/customers/customers-stats'
import { BottomNav } from '@/components/navigation/bottom-nav'
import { FabSpeedDial } from '@/components/navigation/fab-speed-dial'
import { useRouter } from 'next/navigation'

export function CustomersContent() {
  const { language } = useLanguage()
  const router = useRouter()
  const {
    customers,
    loading,
    deleteCustomer,
  } = useCustomers()

  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'company' | 'date'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="text-center">
          <div className="w-16 h-16 rounded-2xl brand-gradient flex items-center justify-center mb-4 mx-auto">
            <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
          </div>
          <p className="text-slate-600 font-medium">Loading customers...</p>
        </div>
      </div>
    )
  }

  // Filter and sort customers
  const filteredCustomers = customers
    .filter(customer => {
      const matchesSearch = searchQuery === '' || 
        customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (customer.company && customer.company.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (customer.phone && customer.phone.includes(searchQuery))
      
      return matchesSearch
    })
    .sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'company':
          comparison = (a.company || '').localeCompare(b.company || '')
          break
        case 'date':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          break
      }
      
      return sortOrder === 'asc' ? comparison : -comparison
    })

  const handleAddCustomer = () => {
    router.push('/customers/new')
  }

  const handleAddInvoice = () => {
    // Navigate to invoices page with add dialog open
    router.push('/invoices?add=true')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-slate-100/30 to-emerald-50/20">
      <CustomersHeader />
      
      <main className="clean-layout py-6 pb-20">
        {/* Stats Overview */}
        <CustomersStats
          totalCustomers={customers.length}
          newThisMonth={customers.filter(c => {
            const created = new Date(c.created_at)
            const now = new Date()
            return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear()
          }).length}
        />

        {/* Filters and Search */}
        <CustomersFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          sortBy={sortBy}
          onSortByChange={setSortBy}
          sortOrder={sortOrder}
          onSortOrderChange={setSortOrder}
          totalCount={filteredCustomers.length}
          onAddCustomer={handleAddCustomer}
        />

        {/* Customers Grid */}
        <CustomersGrid
          customers={filteredCustomers}
          onDeleteCustomer={deleteCustomer}
          searchQuery={searchQuery}
        />
      </main>

      {/* Bottom Navigation */}
      <BottomNav />
      
      {/* Floating Action Button */}
      <FabSpeedDial 
        onAddInvoice={handleAddInvoice}
        onAddCustomer={handleAddCustomer}
      />
    </div>
  )
}
