'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Search, Plus, SortAsc, SortDesc, X, UserPlus } from 'lucide-react'

interface CustomersFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  sortBy: 'name' | 'company' | 'date'
  onSortByChange: (sort: 'name' | 'company' | 'date') => void
  sortOrder: 'asc' | 'desc'
  onSortOrderChange: (order: 'asc' | 'desc') => void
  totalCount: number
  onAddCustomer: () => void
}

export function CustomersFilters({
  searchQuery,
  onSearchChange,
  sortBy,
  onSortByChange,
  sortOrder,
  onSortOrderChange,
  totalCount,
  onAddCustomer,
}: CustomersFiltersProps) {
  return (
    <div className="space-y-4 mb-6">
      {/* Search and Add Button */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search customers by name, email, company, or phone..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 h-12 form-input text-base bg-white/80 backdrop-blur-sm"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSearchChange('')}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-slate-100"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        <Button
          onClick={onAddCustomer}
          className="btn-success-action h-12 px-6 whitespace-nowrap"
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Add Customer
        </Button>
      </div>

      {/* Sort Options and Results Count */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-wrap gap-3 items-center">
          {/* Sort Options */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-slate-600">Sort by:</span>
            <Select value={sortBy} onValueChange={onSortByChange}>
              <SelectTrigger className="w-auto min-w-[100px] bg-white/80 backdrop-blur-sm border-slate-200">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date Added</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="company">Company</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSortOrderChange(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="bg-white/80 backdrop-blur-sm border-slate-200 hover:bg-white"
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </Button>
          </div>

          {/* Clear Search */}
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSearchChange('')}
              className="text-slate-500 hover:text-slate-700"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        {/* Results Count */}
        <Badge variant="secondary" className="bg-slate-100 text-slate-700 font-medium">
          {totalCount} {totalCount === 1 ? 'customer' : 'customers'}
        </Badge>
      </div>
    </div>
  )
}