'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { AddInvoiceDialog } from '@/components/dashboard/add-invoice-dialog'
import { useInvoices } from '@/hooks/use-invoices'

export function NewInvoiceClient() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { addInvoice } = useInvoices()
  
  const [dialogOpen, setDialogOpen] = useState(true)
  const preSelectedCustomerId = searchParams.get('customer')

  useEffect(() => {
    if (!dialogOpen) {
      router.push('/invoices')
    }
  }, [dialogOpen, router])

  return (
    <AddInvoiceDialog
      open={dialogOpen}
      onOpenChange={setDialogOpen}
      onAdd={addInvoice}
      preSelectedCustomerId={preSelectedCustomerId || undefined}
    />
  )
}
