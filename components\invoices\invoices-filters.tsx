'use client'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { Search, Plus, SortAsc, SortDesc, Filter, X } from 'lucide-react'

interface InvoicesFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: 'all' | 'paid' | 'pending' | 'delivered'
  onStatusFilterChange: (status: 'all' | 'paid' | 'pending' | 'delivered') => void
  sortBy: 'date' | 'amount' | 'client'
  onSortByChange: (sort: 'date' | 'amount' | 'client') => void
  sortOrder: 'asc' | 'desc'
  onSortOrderChange: (order: 'asc' | 'desc') => void
  totalCount: number
  onAddInvoice: () => void
}

export function InvoicesFilters({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  sortBy,
  onSortByChange,
  sortOrder,
  onSortOrderChange,
  totalCount,
  onAddInvoice,
}: InvoicesFiltersProps) {
  const { language } = useLanguage()

  const hasActiveFilters = searchQuery !== '' || statusFilter !== 'all'

  const clearFilters = () => {
    onSearchChange('')
    onStatusFilterChange('all')
  }

  return (
    <div className="space-y-4 mb-6">
      {/* Search and Add Button */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search invoices, clients, or invoice numbers..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 h-12 form-input text-base bg-white/80 backdrop-blur-sm"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSearchChange('')}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-slate-100"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        <Button
          onClick={onAddInvoice}
          className="btn-primary-action h-12 px-6 whitespace-nowrap"
        >
          <Plus className="h-4 w-4 mr-2" />
          {getTranslation(language, 'addInvoice')}
        </Button>
      </div>

      {/* Filters and Sort */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-wrap gap-3 items-center">
          {/* Status Filter */}
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-slate-500" />
            <Select value={statusFilter} onValueChange={onStatusFilterChange}>
              <SelectTrigger className="w-auto min-w-[120px] bg-white/80 backdrop-blur-sm border-slate-200">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                    <span>{getTranslation(language, 'pending')}</span>
                  </div>
                </SelectItem>
                <SelectItem value="paid">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                    <span>{getTranslation(language, 'paid')}</span>
                  </div>
                </SelectItem>
                <SelectItem value="delivered">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                    <span>{getTranslation(language, 'delivered')}</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Sort Options */}
          <div className="flex items-center space-x-2">
            <Select value={sortBy} onValueChange={onSortByChange}>
              <SelectTrigger className="w-auto min-w-[100px] bg-white/80 backdrop-blur-sm border-slate-200">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="amount">Amount</SelectItem>
                <SelectItem value="client">Client</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSortOrderChange(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="bg-white/80 backdrop-blur-sm border-slate-200 hover:bg-white"
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </Button>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-slate-500 hover:text-slate-700"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        {/* Results Count */}
        <Badge variant="secondary" className="bg-slate-100 text-slate-700 font-medium">
          {totalCount} {totalCount === 1 ? 'invoice' : 'invoices'}
        </Badge>
      </div>
    </div>
  )
}