'use client'

import { CustomerCard } from './customer-card'
import { Customer } from '@/hooks/use-customers'
import { Users, Search } from 'lucide-react'

interface CustomersGridProps {
  customers: Customer[]
  onDeleteCustomer: (id: string) => void
  searchQuery: string
}

export function CustomersGrid({ customers, onDeleteCustomer, searchQuery }: CustomersGridProps) {
  if (customers.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-20 h-20 rounded-2xl bg-slate-100 flex items-center justify-center mx-auto mb-6">
          {searchQuery ? (
            <Search className="h-10 w-10 text-slate-400" />
          ) : (
            <Users className="h-10 w-10 text-slate-400" />
          )}
        </div>
        
        {searchQuery ? (
          <div>
            <h3 className="text-lg font-semibold text-slate-900 mb-2">No customers found</h3>
            <p className="text-slate-600 font-medium mb-4">
              Try adjusting your search criteria
            </p>
            <div className="flex justify-center">
              <span className="bg-slate-100 px-3 py-1 rounded-full text-sm text-slate-500">
                Search: "{searchQuery}"
              </span>
            </div>
          </div>
        ) : (
          <div>
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              No customers yet
            </h3>
            <p className="text-slate-600 font-medium">
              Add your first customer to get started
            </p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {customers.map((customer) => (
        <CustomerCard
          key={customer.id}
          customer={customer}
          onDelete={onDeleteCustomer}
        />
      ))}
    </div>
  )
}