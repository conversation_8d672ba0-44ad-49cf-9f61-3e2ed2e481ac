'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { Invoice } from '@/hooks/use-invoices'
import { QrCode, CheckCircle, Clock, Package, Calendar, FileText, Hash } from 'lucide-react'
import { format } from 'date-fns'
import { QRPaymentDialog } from './qr-payment-dialog'
import { useState } from 'react'

interface InvoiceCardProps {
  invoice: Invoice
  onUpdateStatus: (id: string, status: 'paid' | 'delivered') => void
}

export function InvoiceCard({ invoice, onUpdateStatus }: InvoiceCardProps) {
  const { language } = useLanguage()
  const [qrDialogOpen, setQrDialogOpen] = useState(false)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'delivered':
        return <Package className="h-4 w-4" />
      default:
        return null
    }
  }

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'paid':
        return 'status-paid border font-medium'
      case 'pending':
        return 'status-pending border font-medium'
      case 'delivered':
        return 'status-delivered border font-medium'
      default:
        return 'bg-slate-100 text-slate-800 border border-slate-200 font-medium'
    }
  }

  return (
    <>
      <Card className="professional-card card-hover border-0 shadow-professional bg-white/90 backdrop-blur-sm overflow-hidden">
        <CardHeader className="professional-card-header pb-3">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h3 className="font-bold text-lg text-slate-900">{invoice.client_name}</h3>
                {invoice.invoice_number && (
                  <div className="flex items-center space-x-1 bg-slate-100 px-2 py-1 rounded-md">
                    <Hash className="h-3 w-3 text-slate-500" />
                    <span className="text-xs font-bold text-slate-600">{invoice.invoice_number}</span>
                  </div>
                )}
              </div>
              <p className="text-sm text-slate-600 line-clamp-2 font-medium">{invoice.service_description}</p>
            </div>
            <Badge className={`${getStatusStyle(invoice.status)} ml-3 flex-shrink-0`}>
              <div className="flex items-center space-x-1">
                {getStatusIcon(invoice.status)}
                <span className="font-bold">{getTranslation(language, invoice.status)}</span>
              </div>
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="professional-card-content space-y-4 pt-4">
          <div className="flex justify-between items-center">
            <div className="amount-display text-2xl font-bold text-slate-900">₹{invoice.amount.toLocaleString()}</div>
            <div className="flex items-center text-sm text-slate-500 font-medium">
              <Calendar className="h-4 w-4 mr-1" />
              {format(new Date(invoice.invoice_date), 'MMM dd, yyyy')}
            </div>
          </div>

          {invoice.due_date && (
            <div className="flex items-center text-sm text-slate-600 bg-amber-50 p-3 rounded-lg border border-amber-200">
              <Clock className="h-4 w-4 mr-2 text-amber-600" />
              <span className="font-bold">{getTranslation(language, 'dueDate')}:</span>
              <span className="ml-1 font-medium">{format(new Date(invoice.due_date), 'MMM dd, yyyy')}</span>
            </div>
          )}

          {invoice.notes && (
            <div className="flex items-start text-sm text-slate-600 bg-blue-50 p-3 rounded-lg border border-blue-200">
              <FileText className="h-4 w-4 mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <span className="font-bold">{getTranslation(language, 'notes')}:</span>
                <p className="mt-1 font-medium">{invoice.notes}</p>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-0 bg-slate-50/50">
          <div className="flex gap-2 w-full">
            {invoice.status === 'pending' && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQrDialogOpen(true)}
                  className="flex-1 border-blue-200 hover:bg-blue-50 hover:border-blue-400 font-medium"
                >
                  <QrCode className="h-4 w-4 mr-2" />
                  {getTranslation(language, 'generateQr')}
                </Button>
                <Button
                  size="sm"
                  onClick={() => onUpdateStatus(invoice.id, 'paid')}
                  className="flex-1 btn-success-action"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {getTranslation(language, 'markAsPaid')}
                </Button>
              </>
            )}
            
            {invoice.status === 'paid' && (
              <Button
                size="sm"
                onClick={() => onUpdateStatus(invoice.id, 'delivered')}
                className="w-full btn-primary-action"
              >
                <Package className="h-4 w-4 mr-2" />
                {getTranslation(language, 'markAsDelivered')}
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>

      <QRPaymentDialog
        open={qrDialogOpen}
        onOpenChange={setQrDialogOpen}
        invoice={invoice}
      />
    </>
  )
}