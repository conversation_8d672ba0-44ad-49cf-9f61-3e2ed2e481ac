'use client'

import { useRouter, usePathname } from 'next/navigation'
import { Home, FileText, Users, User } from 'lucide-react'
import { cn } from '@/lib/utils'

interface NavItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  ariaLabel: string
  implemented: boolean
}

interface BottomNavProps {
  className?: string
}

export function BottomNav({ className }: BottomNavProps) {
  const router = useRouter()
  const pathname = usePathname()
  
  const navItems: NavItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      href: '/',
      ariaLabel: 'Navigate to home dashboard',
      implemented: true
    },
    {
      id: 'invoices',
      label: 'Invoices',
      icon: FileText,
      href: '/invoices',
      ariaLabel: 'Navigate to invoices list',
      implemented: true
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: Users,
      href: '/customers',
      ariaLabel: 'Navigate to customers management',
      implemented: true
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      href: '/profile',
      ariaLabel: 'Navigate to user profile',
      implemented: false
    }
  ]

  const handleNavigation = (item: NavItem) => {
    if (item.implemented) {
      router.push(item.href)
    } else {
      // For unimplemented routes, we could show a toast or modal
      console.log(`${item.label} feature coming soon!`)
    }
  }

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/' || pathname === ''
    }
    return pathname.startsWith(href)
  }

  return (
    <nav 
      className={cn(
        "fixed bottom-0 left-0 right-0 z-40",
        "bg-white/95 backdrop-blur-sm border-t border-slate-200",
        "shadow-lg safe-area-pb",
        className
      )}
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="clean-layout">
        <div className="flex items-center justify-around h-16 px-2">
          {/* Navigation Items */}
          {navItems.map((item) => {
            const Icon = item.icon
            const active = isActive(item.href)
            
            return (
              <button
                key={item.id}
                onClick={() => handleNavigation(item)}
                className={cn(
                  // Base styles
                  "flex flex-col items-center justify-center",
                  "min-h-[44px] min-w-[44px] px-4 py-2",
                  "rounded-xl transition-all duration-200 ease-in-out",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                  "touch-target flex-1 relative",
                  // Active state
                  active
                    ? "bg-blue-50 text-blue-600 scale-105"
                    : "text-slate-500 hover:text-slate-700 hover:bg-slate-50",
                  // Smooth transitions
                  "transform hover:scale-105 active:scale-95",
                  // Disabled state for unimplemented features
                  !item.implemented && "opacity-75"
                )}
                type="button"
                role="tab"
                aria-selected={active}
                aria-label={item.ariaLabel}
                aria-describedby={`${item.id}-description`}
              >
                <Icon 
                  className={cn(
                    "h-5 w-5 mb-1 transition-all duration-200",
                    active ? "text-blue-600" : "text-slate-500"
                  )} 
                  aria-hidden="true"
                />
                <span 
                  className={cn(
                    "text-xs font-medium transition-all duration-200",
                    "leading-tight text-center",
                    active ? "text-blue-600 font-semibold" : "text-slate-500"
                  )}
                >
                  {item.label}
                </span>
                
                {/* Coming soon indicator for unimplemented features */}
                {!item.implemented && !active && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-amber-400 rounded-full animate-pulse" aria-hidden="true" />
                )}
                
                {/* Screen reader descriptions */}
                <span id={`${item.id}-description`} className="sr-only">
                  {active 
                    ? `Currently viewing ${item.label}` 
                    : item.implemented 
                      ? `Navigate to ${item.label}` 
                      : `${item.label} feature coming soon`
                  }
                </span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Active indicator */}
      <div 
        className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-blue-600"
        aria-hidden="true"
      />
    </nav>
  )
}

// Export individual navigation hook for programmatic navigation
export function useBottomNavigation() {
  const router = useRouter()
  const pathname = usePathname()

  const navigateTo = (href: string) => {
    router.push(href)
  }

  const isCurrentRoute = (href: string) => {
    if (href === '/') {
      return pathname === '/' || pathname === ''
    }
    return pathname.startsWith(href)
  }

  return {
    navigateTo,
    isCurrentRoute,
    currentPath: pathname
  }
}