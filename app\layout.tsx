import './globals.css'
 
import type { Metadata } from 'next'

import { Inter } from 'next/font/google'

import { Toaster } from '@/components/ui/sonner'
import { LanguageProvider } from '@/contexts/language-context'
import { AuthProvider } from '@/contexts/auth-context'

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  weight: ['400', '500', '700'],
})

export const metadata: Metadata = {
  title: 'InstraBillBoi - Smart Invoice Management',
  description: 'Professional invoice and payment management for modern businesses',
  keywords: 'invoice, billing, payment, business, management, UPI, QR code',
  authors: [{ name: 'InstraBillBoi Team' }],
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'InstraBillBoi'
  }
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#3F51B5',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="InstraBillBoi" />
      </head>
      <body className={`${inter.className} font-sans antialiased`}>
        <AuthProvider>
          <LanguageProvider>
            {children}
            <Toaster 
              position="top-center"
              toastOptions={{
                style: {
                  background: 'hsl(var(--card))',
                  color: 'hsl(var(--card-foreground))',
                  border: '1px solid hsl(var(--border))',
                },
              }}
            />
          </LanguageProvider>
        </AuthProvider>
      </body>
    </html>
  )
}