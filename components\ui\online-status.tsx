'use client'

import { Wifi, WifiOff } from 'lucide-react'
import { useOffline } from '@/hooks/use-offline'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { Badge } from './badge'

export function OnlineStatus() {
  const isOffline = useOffline()
  const { language } = useLanguage()

  if (isOffline) {
    return (
      <Badge variant="destructive" className="text-xs flex items-center space-x-1 font-medium">
        <WifiOff className="h-3 w-3" />
        <span>{getTranslation(language, 'offlineMode')}</span>
      </Badge>
    )
  }

  return (
    <Badge className="text-xs flex items-center space-x-1 bg-emerald-100 text-emerald-700 border-emerald-200 font-medium">
      <Wifi className="h-3 w-3" />
      <span>Online</span>
    </Badge>
  )
}
