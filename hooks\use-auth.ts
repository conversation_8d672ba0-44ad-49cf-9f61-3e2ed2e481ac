import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export function useAuth() {
  const [loading, setLoading] = useState(false)

  const signInWithEmailAndPassword = async (email: string, password: string) => {
    setLoading(true)
    const { error } = await supabase.auth.signInWithPassword({ email, password })
    setLoading(false)
    if (error) {
      toast.error(error.message)
      return false
    }
    toast.success('Welcome back!')
    return true
  }

  const signUpWithEmailAndPassword = async (email: string, password: string) => {
    setLoading(true)
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}`,
      },
    })
    setLoading(false)
    if (error) {
      toast.error(error.message)
      return false
    }
    toast.success('Account created! Please check your email to verify.')
    return true
  }

  const sendMagicLink = async (email: string) => {
    setLoading(true)
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${window.location.origin}`,
      },
    })
    setLoading(false)
    if (error) {
      toast.error(error.message)
      return false
    }
    toast.success('Magic link sent! Check your email.')
    return true
  }

  const sendPasswordResetEmail = async (email: string) => {
    setLoading(true)
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/update-password`, // You might want a dedicated page for password reset
    })
    setLoading(false)
    if (error) {
      toast.error(error.message)
      return false
    }
    toast.success('Password reset email sent! Check your inbox.')
    return true
  }

  return {
    loading,
    signInWithEmailAndPassword,
    signUpWithEmailAndPassword,
    sendMagicLink,
    sendPasswordResetEmail,
  }
}
