'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useCustomers, Customer } from '@/hooks/use-customers'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { 
  ArrowLeft, 
  User, 
  Mail, 
  Phone, 
  Building2, 
  MapPin, 
  FileText,
  Save,
  AlertCircle,
  UserPlus
} from 'lucide-react'
import { toast } from 'sonner'

interface FormData {
  name: string
  email: string
  phone: string
  company: string
  address_line_1: string
  address_line_2: string
  city: string
  state: string
  postal_code: string
  country: string
  notes: string
}

interface FormErrors {
  name?: string
  email?: string
  phone?: string
}

export function NewCustomerForm() {
  const router = useRouter()
  const { language } = useLanguage()
  const { addCustomer } = useCustomers()
  const [loading, setLoading] = useState(false)
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'India',
    notes: '',
  })

  const [errors, setErrors] = useState<FormErrors>({})

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters'
    }

    // Email validation (optional but must be valid if provided)
    if (formData.email && formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address'
      }
    }

    // Phone validation (optional but must be valid if provided)
    if (formData.phone && formData.phone.trim()) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      if (!phoneRegex.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
        newErrors.phone = 'Please enter a valid phone number'
      }
    }

    // At least one contact method required
    if (!formData.email.trim() && !formData.phone.trim()) {
      if (!newErrors.email) newErrors.email = 'Email or phone number is required'
      if (!newErrors.phone) newErrors.phone = 'Email or phone number is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('Please fix the errors before submitting')
      return
    }

    setLoading(true)

    try {
      // Clean up the data - remove empty strings and convert to null
      const customerData: Omit<Customer, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
        name: formData.name.trim(), // name is always a string
        email: formData.email.trim() || null,
        phone: formData.phone.trim() || null,
        company: formData.company.trim() || null,
        address_line_1: formData.address_line_1.trim() || null,
        address_line_2: formData.address_line_2.trim() || null,
        city: formData.city.trim() || null,
        state: formData.state.trim() || null,
        postal_code: formData.postal_code.trim() || null,
        country: formData.country.trim() || null,
        notes: formData.notes.trim() || null,
      }

      await addCustomer(customerData)
      
      toast.success('Customer added successfully!')
      router.push('/customers')
    } catch (error) {
      // Error is already handled in the hook
      console.error('Failed to add customer:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-slate-100/30 to-emerald-50/20">
      {/* Header */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-50 shadow-sm">
        <div className="clean-layout">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/customers')}
                className="hover:bg-slate-100 p-2"
                aria-label="Go back to customers"
              >
                <ArrowLeft className="h-5 w-5 text-slate-600" />
              </Button>
              
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-xl success-gradient flex items-center justify-center">
                  <UserPlus className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-slate-900">Add New Customer</h1>
                  <p className="text-xs text-slate-500 -mt-1 font-medium">
                    Create a new customer profile
                  </p>
                </div>
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/customers')}
              className="border-slate-200 hover:border-slate-400 transition-colors"
            >
              Cancel
            </Button>
          </div>
        </div>
      </header>

      {/* Form */}
      <main className="clean-layout py-6">
        <div className="max-w-2xl mx-auto">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card className="professional-card bg-white/90 backdrop-blur-sm border-0 shadow-professional">
              <CardHeader className="professional-card-header">
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-slate-600" />
                  <span>Basic Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="professional-card-content space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="form-label flex items-center">
                      Customer Name
                      <span className="text-red-500 ml-1" aria-label="required">*</span>
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={`h-12 form-input text-base ${
                        errors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                      }`}
                      placeholder="Enter customer name"
                      required
                      aria-invalid={errors.name ? 'true' : 'false'}
                      aria-describedby={errors.name ? 'name-error' : undefined}
                    />
                    {errors.name && (
                      <div id="name-error" className="flex items-center space-x-1 text-sm text-red-600 font-medium" role="alert">
                        <AlertCircle className="h-4 w-4 flex-shrink-0" />
                        <span>{errors.name}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company" className="form-label flex items-center">
                      <Building2 className="h-4 w-4 mr-2 text-slate-500" />
                      Company
                    </Label>
                    <Input
                      id="company"
                      value={formData.company}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                      className="h-12 form-input text-base"
                      placeholder="Company name (optional)"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="professional-card bg-white/90 backdrop-blur-sm border-0 shadow-professional">
              <CardHeader className="professional-card-header">
                <CardTitle className="flex items-center space-x-2">
                  <Phone className="h-5 w-5 text-slate-600" />
                  <span>Contact Information</span>
                </CardTitle>
                <p className="text-sm text-slate-600 font-medium">At least one contact method is required</p>
              </CardHeader>
              <CardContent className="professional-card-content space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="form-label flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-slate-500" />
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className={`h-12 form-input text-base ${
                        errors.email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                      }`}
                      placeholder="<EMAIL>"
                      aria-invalid={errors.email ? 'true' : 'false'}
                      aria-describedby={errors.email ? 'email-error' : undefined}
                    />
                    {errors.email && (
                      <div id="email-error" className="flex items-center space-x-1 text-sm text-red-600 font-medium" role="alert">
                        <AlertCircle className="h-4 w-4 flex-shrink-0" />
                        <span>{errors.email}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone" className="form-label flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-slate-500" />
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className={`h-12 form-input text-base ${
                        errors.phone ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                      }`}
                      placeholder="+91 98765 43210"
                      aria-invalid={errors.phone ? 'true' : 'false'}
                      aria-describedby={errors.phone ? 'phone-error' : undefined}
                    />
                    {errors.phone && (
                      <div id="phone-error" className="flex items-center space-x-1 text-sm text-red-600 font-medium" role="alert">
                        <AlertCircle className="h-4 w-4 flex-shrink-0" />
                        <span>{errors.phone}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Address Information */}
            <Card className="professional-card bg-white/90 backdrop-blur-sm border-0 shadow-professional">
              <CardHeader className="professional-card-header">
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-slate-600" />
                  <span>Address Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="professional-card-content space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="address_line_1" className="form-label">
                      Address Line 1
                    </Label>
                    <Input
                      id="address_line_1"
                      value={formData.address_line_1}
                      onChange={(e) => handleInputChange('address_line_1', e.target.value)}
                      className="h-12 form-input text-base"
                      placeholder="Street address"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address_line_2" className="form-label">
                      Address Line 2
                    </Label>
                    <Input
                      id="address_line_2"
                      value={formData.address_line_2}
                      onChange={(e) => handleInputChange('address_line_2', e.target.value)}
                      className="h-12 form-input text-base"
                      placeholder="Apartment, suite, etc. (optional)"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city" className="form-label">
                        City
                      </Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        className="h-12 form-input text-base"
                        placeholder="City"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="state" className="form-label">
                        State
                      </Label>
                      <Input
                        id="state"
                        value={formData.state}
                        onChange={(e) => handleInputChange('state', e.target.value)}
                        className="h-12 form-input text-base"
                        placeholder="State"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="postal_code" className="form-label">
                        Postal Code
                      </Label>
                      <Input
                        id="postal_code"
                        value={formData.postal_code}
                        onChange={(e) => handleInputChange('postal_code', e.target.value)}
                        className="h-12 form-input text-base"
                        placeholder="PIN Code"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="country" className="form-label">
                      Country
                    </Label>
                    <Input
                      id="country"
                      value={formData.country}
                      onChange={(e) => handleInputChange('country', e.target.value)}
                      className="h-12 form-input text-base"
                      placeholder="Country"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Information */}
            <Card className="professional-card bg-white/90 backdrop-blur-sm border-0 shadow-professional">
              <CardHeader className="professional-card-header">
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-slate-600" />
                  <span>Additional Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="professional-card-content">
                <div className="space-y-2">
                  <Label htmlFor="notes" className="form-label">
                    Notes
                  </Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    rows={4}
                    className="form-input resize-none text-base"
                    placeholder="Additional notes about the customer (optional)"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Submit Button */}
            <div className="flex space-x-4 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => router.push('/customers')} 
                className="flex-1 h-12 border-slate-200 hover:bg-slate-50 font-medium"
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={loading} 
                className="btn-success-action flex-1 h-12"
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span className="font-bold">Adding Customer...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Save className="h-4 w-4" />
                    <span className="font-bold">Add Customer</span>
                  </div>
                )}
              </Button>
            </div>
          </form>
        </div>
      </main>
    </div>
  )
}