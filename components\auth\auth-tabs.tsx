'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { CardTitle, CardDescription } from '@/components/ui/card'

interface AuthTabsProps {
  activeTab: string
  onTabChange: (value: string) => void
}

export function AuthTabs({ activeTab, onTabChange }: AuthTabsProps) {
  return (
    <Tabs
      value={activeTab}
      onValueChange={onTabChange}
      className="w-full"
      aria-label="Authentication method selection"
    >
      <TabsList className="grid w-full grid-cols-2 bg-slate-100 rounded-2xl p-1 mb-6" role="tablist">
        <TabsTrigger
          value="signin"
          className="rounded-xl font-semibold data-[state=active]:bg-white data-[state=active]:shadow-sm h-12 text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          role="tab"
          aria-selected={activeTab === 'signin'}
          aria-controls="signin-panel"
        >
          Sign In
        </TabsTrigger>
        <TabsTrigger
          value="signup"
          className="rounded-xl font-semibold data-[state=active]:bg-white data-[state=active]:shadow-sm h-12 text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          role="tab"
          aria-selected={activeTab === 'signup'}
          aria-controls="signup-panel"
        >
          Sign Up
        </TabsTrigger>
      </TabsList>

      <TabsContent value="signin" className="space-y-0" id="signin-panel" role="tabpanel" aria-labelledby="signin-tab">
        <CardTitle className="text-2xl font-bold text-slate-900 mb-2">
          Welcome Back
        </CardTitle>
        <CardDescription className="text-slate-600 text-base">
          Sign in to your account to continue
        </CardDescription>
      </TabsContent>

      <TabsContent value="signup" className="space-y-0" id="signup-panel" role="tabpanel" aria-labelledby="signup-tab">
        <CardTitle className="text-2xl font-bold text-slate-900 mb-2">
          Create Account
        </CardTitle>
        <CardDescription className="text-slate-600 text-base">
          Join thousands of businesses using InstraBillBoi
        </CardDescription>
      </TabsContent>
    </Tabs>
  )
}
