'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/hooks/use-auth'
import { Loader2, KeyRound, LogIn, UserPlus, ArrowRight, Sparkles } from 'lucide-react';

import { AuthBranding } from './auth-branding'
import { AuthFooter } from './auth-footer'
import { MagicLinkSentMessage } from './magic-link-sent-message'
import { AuthTabs } from './auth-tabs'
import { AuthMethodToggle } from './auth-method-toggle'
import { MagicLinkForm } from './magic-link-form'
import { PasswordAuthForm } from './password-auth-form'
import { ForgotPasswordForm } from './forgot-password-form'

// Zod <PERSON>
const signInSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters.' }),
})

const signUpSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters.' }),
  confirmPassword: z.string().min(6, { message: 'Please confirm your password.' }),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match.',
  path: ['confirmPassword'],
})

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
})

type FormData = z.infer<typeof signInSchema> | z.infer<typeof signUpSchema> | z.infer<typeof forgotPasswordSchema>

export function LoginCard() {
  const [activeTab, setActiveTab] = useState<'signin' | 'signup'>('signin')
  const [authMethod, setAuthMethod] = useState<'password' | 'magic'>('password')
  const [magicLinkSent, setMagicLinkSent] = useState(false)
  const [forgotPasswordMode, setForgotPasswordMode] = useState(false)

  const { loading, signInWithEmailAndPassword, signUpWithEmailAndPassword, sendMagicLink, sendPasswordResetEmail } = useAuth()

  const form = useForm<FormData>({
    resolver: zodResolver(
      forgotPasswordMode ? forgotPasswordSchema :
      (activeTab === 'signin' ? signInSchema : signUpSchema)
    ),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
    },
  })

  const { handleSubmit, reset, watch } = form
  const emailWatch = watch('email')

  const handleAuthSubmit = async (values: FormData) => {
    if (forgotPasswordMode) {
      const forgotPasswordValues = values as z.infer<typeof forgotPasswordSchema>
      await sendPasswordResetEmail(forgotPasswordValues.email)
    } else if (authMethod === 'magic') {
      const magicLinkValues = values as z.infer<typeof signInSchema>
      const success = await sendMagicLink(magicLinkValues.email)
      if (success) {
        setMagicLinkSent(true)
      }
    } else { // password method
      if (activeTab === 'signin') {
        const signInValues = values as z.infer<typeof signInSchema>
        await signInWithEmailAndPassword(signInValues.email, signInValues.password!)
      } else {
        const signUpValues = values as z.infer<typeof signUpSchema>
        await signUpWithEmailAndPassword(signUpValues.email, signUpValues.password)
      }
    }
  }

  const resetFormAndState = () => {
    reset()
    setMagicLinkSent(false)
    setForgotPasswordMode(false)
    setAuthMethod('magic')
  }

  if (magicLinkSent) {
    return <MagicLinkSentMessage onSendAnotherLink={() => setMagicLinkSent(false)} />
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-100 via-slate-200/60 to-slate-300/40">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(30,41,59,0.08)_0%,transparent_50%)] pointer-events-none" aria-hidden="true"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(71,85,105,0.06)_0%,transparent_50%)] pointer-events-none" aria-hidden="true"></div>

      <div className="w-full max-w-md relative">
        <AuthBranding />

        <Card className="bg-white/95 backdrop-blur-sm border-0 shadow-professional-lg rounded-3xl overflow-hidden" role="main">
          <CardHeader className="text-center pb-6 pt-8 px-8">
            {!forgotPasswordMode ? (
              <AuthTabs
                activeTab={activeTab}
                onTabChange={(value) => { setActiveTab(value as 'signin' | 'signup'); resetFormAndState(); }}
              />
            ) : (
              <div className="text-center">
                <h2 className="text-2xl font-bold text-slate-900 mb-2">Forgot Password?</h2>
                <p className="text-slate-600 text-base">Enter your email to receive a password reset link.</p>
              </div>
            )}
          </CardHeader>

          <CardContent className="px-8 pb-8">
            <FormProvider {...form}>
              <form onSubmit={handleSubmit(handleAuthSubmit)} noValidate>
                {!forgotPasswordMode && (
                  <AuthMethodToggle
                    authMethod={authMethod}
                    setAuthMethod={setAuthMethod}
                    onMethodChange={() => reset()}
                  />
                )}

                {!forgotPasswordMode && authMethod === 'magic' && (
                  <MagicLinkForm />
                )}

                {!forgotPasswordMode && authMethod === 'password' && (
                  <PasswordAuthForm
                    activeTab={activeTab}
                    onForgotPasswordClick={() => { setForgotPasswordMode(true); reset({ email: emailWatch }); }}
                  />
                )}

                {forgotPasswordMode && (
                  <ForgotPasswordForm
                    onBackToSignIn={() => { setForgotPasswordMode(false); reset({ email: emailWatch }); }}
                  />
                )}

                <Button
                  type="submit"
                  disabled={loading}
                  className="w-full h-14 text-base font-bold action-gradient hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-describedby="submit-button-help"
                >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-5 w-5 animate-spin" aria-hidden="true" />
                    <span>{forgotPasswordMode ? 'Sending Reset Link...' : (authMethod === 'magic' ? 'Sending Magic Link...' : (activeTab === 'signin' ? 'Signing in...' : 'Creating account...'))}</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    {forgotPasswordMode ? (
                      <KeyRound className="h-5 w-5" aria-hidden="true" />
                    ) : authMethod === 'magic' ? (
                      <Sparkles className="h-5 w-5" aria-hidden="true" />
                    ) : activeTab === 'signin' ? (
                      <LogIn className="h-5 w-5" aria-hidden="true" />
                    ) : (
                      <UserPlus className="h-5 w-5" aria-hidden="true" />
                    )}
                    <span>{forgotPasswordMode ? 'Send Reset Link' : (authMethod === 'magic' ? 'Send me magic link' : (activeTab === 'signin' ? 'Sign In' : 'Create Account'))}</span>
                    <ArrowRight className="h-5 w-5" aria-hidden="true" />
                  </div>
                )}
                </Button>
                <p id="submit-button-help" className="sr-only">
                  {forgotPasswordMode
                    ? 'Click to send a password reset link to your email address'
                    : (activeTab === 'signin'
                      ? 'Click to sign in to your account'
                      : 'Click to create your new account')
                  }
                </p>
              </form>
            </FormProvider>
          </CardContent>
        </Card>

        <AuthFooter />
      </div>
    </div>
  )
}