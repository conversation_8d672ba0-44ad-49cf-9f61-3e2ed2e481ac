'use client'

import { Suspense } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { PhoneAuth } from '@/components/auth/phone-auth'
import { NewInvoiceClient } from '@/components/invoices/new-invoice-client'

export default function NewInvoicePage() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return <PhoneAuth />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-slate-100/30 to-blue-50/20">
      <Suspense fallback={<div>Loading invoice form...</div>}>
        <NewInvoiceClient />
      </Suspense>
    </div>
  )
}
