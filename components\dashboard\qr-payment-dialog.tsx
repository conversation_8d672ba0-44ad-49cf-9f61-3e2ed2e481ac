'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { Invoice } from '@/hooks/use-invoices'
import { useEffect, useState } from 'react'
import QRCode from 'qrcode'
import { Smartphone, Copy, QrCode, Building2, Hash } from 'lucide-react'
import { toast } from 'sonner'

interface QRPaymentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  invoice: Invoice
}

export function QRPaymentDialog({ open, onOpenChange, invoice }: QRPaymentDialogProps) {
  const { language } = useLanguage()
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [loading, setLoading] = useState(false)

  // Mock UPI ID - in real app, this would come from user profile
  const upiId = 'donotpayme@notaupi'

  useEffect(() => {
    if (open) {
      generateQRCode()
    }
  }, [open, invoice])

  const generateQRCode = async () => {
    setLoading(true)
    try {
      // Include invoice number in the transaction note for better reference
      const transactionNote = `Payment for ${invoice.service_description} - Invoice: ${invoice.invoice_number || 'N/A'}`
      const upiUrl = `upi://pay?pa=${upiId}&pn=${encodeURIComponent(invoice.client_name)}&am=${invoice.amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}`
      
      const qrDataUrl = await QRCode.toDataURL(upiUrl, {
        width: 280,
        margin: 2,
        color: {
          dark: '#1e293b',
          light: '#ffffff',
        },
      })
      
      setQrCodeUrl(qrDataUrl)
    } catch (error) {
      console.error('Error generating QR code:', error)
      toast.error('Failed to generate QR code')
    } finally {
      setLoading(false)
    }
  }

  const copyUpiId = () => {
    navigator.clipboard.writeText(upiId)
    toast.success('UPI ID copied to clipboard')
  }

  const copyInvoiceNumber = () => {
    if (invoice.invoice_number) {
      navigator.clipboard.writeText(invoice.invoice_number)
      toast.success('Invoice number copied to clipboard')
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md professional-card bg-white/95 backdrop-blur-sm border-0 shadow-professional-lg">
        <DialogHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 rounded-2xl action-gradient flex items-center justify-center">
              <QrCode className="h-6 w-6 text-white" />
            </div>
          </div>
          <DialogTitle className="text-2xl font-bold text-slate-900">
            {getTranslation(language, 'upiPayment')}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="text-center">
            <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl p-6 mb-6 border border-slate-200">
              <div className="amount-large text-3xl font-bold text-slate-900 mb-2">₹{invoice.amount.toLocaleString()}</div>
              <div className="text-sm text-slate-600 mb-1 font-medium">{invoice.service_description}</div>
              <div className="text-xs text-slate-500 font-medium mb-2">{getTranslation(language, 'client')}: {invoice.client_name}</div>
              
              {/* Invoice Number Display */}
              {invoice.invoice_number && (
                <div className="flex items-center justify-center space-x-2 bg-white/60 rounded-lg px-3 py-2 border border-slate-200">
                  <Hash className="h-4 w-4 text-slate-500" />
                  <span className="text-sm font-bold text-slate-700">Invoice: {invoice.invoice_number}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={copyInvoiceNumber}
                    className="h-6 w-6 p-0 hover:bg-slate-200"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          <div className="text-center">
            {loading ? (
              <div className="flex items-center justify-center h-72 bg-slate-50 rounded-2xl">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-2xl action-gradient flex items-center justify-center mx-auto mb-4">
                    <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  </div>
                  <p className="text-slate-600 font-medium">Generating QR Code...</p>
                </div>
              </div>
            ) : (
              qrCodeUrl && (
                <div className="space-y-4">
                  <div className="bg-white p-6 rounded-2xl border-2 border-slate-200 inline-block">
                    <img
                      src={qrCodeUrl}
                      alt={`UPI QR Code for Invoice ${invoice.invoice_number || 'N/A'}`}
                      className="mx-auto rounded-xl"
                    />
                  </div>
                  <p className="text-sm text-slate-600 bg-blue-50 p-3 rounded-lg border border-blue-200 font-medium">
                    <QrCode className="h-4 w-4 inline mr-2" />
                    {getTranslation(language, 'scanQrCode')}
                  </p>
                  {invoice.invoice_number && (
                    <p className="text-xs text-slate-500 font-medium">
                      Reference: {invoice.invoice_number}
                    </p>
                  )}
                </div>
              )
            )}
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between p-4 bg-slate-50 rounded-xl border border-slate-200">
              <div>
                <div className="text-sm text-slate-500 mb-1 font-medium">{getTranslation(language, 'payTo')}</div>
                <div className="font-bold text-slate-900">{upiId}</div>
              </div>
              <Button variant="outline" size="sm" onClick={copyUpiId} className="border-slate-200 hover:bg-white font-medium">
                <Copy className="h-4 w-4" />
              </Button>
            </div>

            <Button
              className="btn-primary-action w-full h-12"
              onClick={() => {
                const transactionNote = `Payment for ${invoice.service_description} - Invoice: ${invoice.invoice_number || 'N/A'}`
                const upiUrl = `upi://pay?pa=${upiId}&pn=${encodeURIComponent(invoice.client_name)}&am=${invoice.amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}`
                window.open(upiUrl)
              }}
            >
              <Smartphone className="h-5 w-5 mr-2" />
              {getTranslation(language, 'payNow')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}