'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useLanguage } from '@/contexts/language-context'
import { useAuth } from '@/contexts/auth-context'
import { getTranslation } from '@/lib/i18n'
import { LogOut, Globe, Building2, Wifi, WifiOff } from 'lucide-react'
import { OnlineStatus } from '@/components/ui/online-status'

export function Header() {
  const { language, setLanguage } = useLanguage()
  const { signOut } = useAuth()

  return (
    <header className="bg-white/95 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-50 shadow-sm">
      <div className="clean-layout">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl brand-gradient flex items-center justify-center">
                <Building2 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900">InstraBillBoi</h1>
                <p className="text-xs text-slate-500 -mt-1 font-medium">Professional Invoicing</p>
              </div>
            </div>
            
            <OnlineStatus />
          </div>

          <div className="flex items-center space-x-3">
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger className="w-auto border-slate-200 hover:border-blue-500/50 transition-colors">
                <Globe className="h-4 w-4 mr-2 text-slate-600" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">
                  <div className="flex items-center space-x-2">
                    <span>🇺🇸</span>
                    <span className="font-medium">{getTranslation(language, 'english')}</span>
                  </div>
                </SelectItem>
                <SelectItem value="bn">
                  <div className="flex items-center space-x-2">
                    <span>🇮🇳</span>
                    <span className="font-medium bengali-text">{getTranslation(language, 'bengali')}</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <Button 
              variant="outline" 
              size="sm" 
              onClick={signOut}
              className="border-slate-200 hover:border-red-300 hover:bg-red-50 hover:text-red-600 transition-colors font-medium"
            >
              <LogOut className="h-4 w-4 mr-2" />
              {getTranslation(language, 'logout')}
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
}
