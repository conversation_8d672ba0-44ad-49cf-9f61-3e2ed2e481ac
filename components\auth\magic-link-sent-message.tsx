'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { Mail, CheckCircle } from 'lucide-react'

interface MagicLinkSentMessageProps {
  onSendAnotherLink: () => void
}

export function MagicLinkSentMessage({ onSendAnotherLink }: MagicLinkSentMessageProps) {
  const { language } = useLanguage()

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-100 via-slate-200/60 to-slate-300/40">
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(30,41,59,0.08)_0%,transparent_50%)] pointer-events-none"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(71,85,105,0.06)_0%,transparent_50%)] pointer-events-none"></div>

      <div className="w-full max-w-md relative">
        <Card className="bg-white/95 backdrop-blur-sm border-0 shadow-professional-lg rounded-3xl overflow-hidden">
          <CardHeader className="text-center pb-6 pt-8 px-8">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 rounded-2xl success-gradient flex items-center justify-center shadow-lg" role="img" aria-label="Email sent successfully">
                <Mail className="h-8 w-8 text-white" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-slate-900 mb-2">
              {getTranslation(language, 'emailSent')}
            </CardTitle>
            <CardDescription className="text-slate-600 text-base leading-relaxed">
              {getTranslation(language, 'checkEmailInbox')}
            </CardDescription>
          </CardHeader>

          <CardContent className="px-8 pb-8">
            <div className="space-y-4">
              <div className="bg-emerald-50 border border-emerald-200 rounded-2xl p-4" role="status" aria-live="polite">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-emerald-600 flex-shrink-0" aria-hidden="true" />
                  <p className="text-emerald-800 text-sm font-medium">
                    {getTranslation(language, 'clickLinkToLogin')}
                  </p>
                </div>
              </div>

              <div className="text-center">
                <p className="text-slate-500 text-sm mb-4">
                  {getTranslation(language, 'checkSpamFolder')}
                </p>
                <Button
                  variant="outline"
                  onClick={onSendAnotherLink}
                  className="border-slate-200 hover:bg-slate-50 font-medium h-12 px-6 rounded-xl"
                  aria-describedby="send-another-description"
                >
                  {getTranslation(language, 'sendAnotherLink')}
                </Button>
                <p id="send-another-description" className="sr-only">
                  Click to return to the login form and send another magic link
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
