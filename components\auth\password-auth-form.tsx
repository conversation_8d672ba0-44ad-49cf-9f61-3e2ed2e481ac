'use client'

import { useState } from 'react'
import { useFormContext, FieldErrors } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Mail, Lock, Eye, EyeOff, LogIn, UserPlus, Loader2, ArrowRight, AlertCircle } from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'
import * as z from 'zod'

interface PasswordAuthFormProps {
  activeTab: 'signin' | 'signup'
  onForgotPasswordClick: () => void
}

const signInSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters.' }),
})

const signUpSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters.' }),
  confirmPassword: z.string().min(6, { message: 'Please confirm your password.' }),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match.',
  path: ['confirmPassword'],
})

type SignInFormData = z.infer<typeof signInSchema>
type SignUpFormData = z.infer<typeof signUpSchema>

export function PasswordAuthForm({ activeTab, onForgotPasswordClick }: PasswordAuthFormProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const { loading } = useAuth()
  const { register, formState: { errors } } = useFormContext<SignInFormData | SignUpFormData>()

  return (
    <>
      {/* Email Field */}
      <div className="space-y-2">
        <Label htmlFor="email-pwd" className="form-label flex items-center text-base">
          <Mail className="h-4 w-4 mr-2 text-slate-500" aria-hidden="true" />
          Email Address
          <span className="text-red-500 ml-1" aria-label="required">*</span>
        </Label>
        <Input
          id="email-pwd"
          type="email"
          inputMode="email"
          autoComplete="email"
          placeholder="Enter your email"
          {...register('email')}
          className={`h-14 text-base border-2 rounded-2xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
            errors.email
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'form-input'
          }`}
          style={{ fontSize: '16px' }}
          required
          aria-invalid={errors.email ? 'true' : 'false'}
          aria-describedby={errors.email ? 'email-pwd-error' : undefined}
        />
        {errors.email && (
          <div id="email-pwd-error" className="flex items-center space-x-1 text-sm text-red-600 font-medium" role="alert">
            <AlertCircle className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
            <span>{errors.email.message}</span>
          </div>
        )}
      </div>

      {/* Password Field */}
      <div className="space-y-2">
        <Label htmlFor="password" className="form-label flex items-center text-base">
          <Lock className="h-4 w-4 mr-2 text-slate-500" aria-hidden="true" />
          Password
          <span className="text-red-500 ml-1" aria-label="required">*</span>
        </Label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            autoComplete={activeTab === 'signin' ? 'current-password' : 'new-password'}
            placeholder="Enter your password"
            {...register('password')}
            className={`h-14 text-base border-2 pr-14 rounded-2xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              errors.password
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'form-input'
            }`}
            style={{ fontSize: '16px' }}
            required
            minLength={6}
            aria-invalid={errors.password ? 'true' : 'false'}
            aria-describedby={errors.password ? 'password-error' : 'password-help'}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-2 text-slate-500 hover:text-slate-700 transition-colors rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
            aria-pressed={showPassword}
            aria-describedby="password-visibility-help"
          >
            {showPassword ? <EyeOff className="h-5 w-5" aria-hidden="true" /> : <Eye className="h-5 w-5" aria-hidden="true" />}
          </button>
          <p id="password-visibility-help" className="sr-only">
            Toggle password visibility
          </p>
        </div>
        {errors.password && (
          <div id="password-error" className="flex items-center space-x-1 text-sm text-red-600 font-medium" role="alert">
            <AlertCircle className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
            <span>{errors.password.message}</span>
          </div>
        )}
        <p id="password-help" className="text-xs text-slate-500">
          Password must be at least 6 characters long
        </p>
      </div>

      {/* Confirm Password Field (Sign Up Only) */}
      {activeTab === 'signup' && (
        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="form-label flex items-center text-base">
            <Lock className="h-4 w-4 mr-2 text-slate-500" aria-hidden="true" />
            Confirm Password
            <span className="text-red-500 ml-1" aria-label="required">*</span>
          </Label>
          <Input
            id="confirmPassword"
            type="password"
            autoComplete="new-password"
            placeholder="Confirm your password"
            {...register('confirmPassword')}
            className={`h-14 text-base border-2 rounded-2xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              (errors as FieldErrors<SignUpFormData>).confirmPassword
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'form-input'
            }`}
            style={{ fontSize: '16px' }}
            required
            aria-invalid={(errors as FieldErrors<SignUpFormData>).confirmPassword ? 'true' : 'false'}
            aria-describedby={(errors as FieldErrors<SignUpFormData>).confirmPassword ? 'confirm-password-error' : 'confirm-password-help'}
          />
          {(errors as FieldErrors<SignUpFormData>).confirmPassword && (
            <div id="confirm-password-error" className="flex items-center space-x-1 text-sm text-red-600 font-medium" role="alert">
              <AlertCircle className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
              <span>{(errors as FieldErrors<SignUpFormData>).confirmPassword?.message}</span>
            </div>
          )}
          <p id="confirm-password-help" className="text-xs text-slate-500">
            Re-enter your password to confirm
          </p>
        </div>
      )}

      {/* Remember Me & Forgot Password (Sign In Only) */}
      {activeTab === 'signin' && (
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center space-x-3">
            <Checkbox
              id="remember"
              checked={rememberMe}
              onCheckedChange={(checked) => {
                if (typeof checked === 'boolean') {
                  setRememberMe(checked)
                }
              }}
              className="rounded-md focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-describedby="remember-help"
            />
            <Label
              htmlFor="remember"
              className="text-sm text-slate-600 font-medium cursor-pointer"
            >
              Remember me
            </Label>
            <p id="remember-help" className="sr-only">
              Keep me signed in on this device
            </p>
          </div>
          <Button
            type="button"
            variant="link"
            onClick={onForgotPasswordClick}
            className="text-sm text-blue-600 hover:text-blue-700 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-1 py-1"
            aria-describedby="forgot-password-help"
          >
            Forgot password?
          </Button>
          <p id="forgot-password-help" className="sr-only">
            Click to reset your password via email
          </p>
        </div>
      )}

    </>
  )
}