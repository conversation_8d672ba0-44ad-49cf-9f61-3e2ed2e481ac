'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { Invoice } from '@/hooks/use-invoices'
import { useCustomers, Customer } from '@/hooks/use-customers'
import { 
  Plus, 
  User, 
  Briefcase, 
  DollarSign, 
  Calendar, 
  FileText, 
  Search,
  Building2,
  Mail,
  Phone,
  MapPin,
  UserPlus,
  X,
  Check,
  AlertCircle
} from 'lucide-react'
import { toast } from 'sonner'

interface AddInvoiceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAdd: (invoice: Omit<Invoice, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => void
  preSelectedCustomerId?: string
}

export function AddInvoiceDialog({ 
  open, 
  onOpenChange, 
  onAdd, 
  preSelectedCustomerId 
}: AddInvoiceDialogProps) {
  const { language } = useLanguage()
  const { customers, loading: customersLoading } = useCustomers()
  
  const [loading, setLoading] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false)
  const [customerSearch, setCustomerSearch] = useState('')
  const [useManualEntry, setUseManualEntry] = useState(false)
  
  const [formData, setFormData] = useState({
    client_name: '',
    service_description: '',
    amount: '',
    due_date: '',
    notes: '',
  })

  const [errors, setErrors] = useState({
    client_name: '',
    service_description: '',
    amount: '',
  })

  // Sort customers alphabetically by name
  const sortedCustomers = customers
    .slice()
    .sort((a, b) => a.name.localeCompare(b.name))

  // Filter customers based on search
  const filteredCustomers = sortedCustomers.filter(customer => {
    const searchLower = customerSearch.toLowerCase()
    return (
      customer.name.toLowerCase().includes(searchLower) ||
      (customer.company && customer.company.toLowerCase().includes(searchLower)) ||
      (customer.email && customer.email.toLowerCase().includes(searchLower))
    )
  })

  // Pre-select customer if provided
  useEffect(() => {
    if (preSelectedCustomerId && customers.length > 0) {
      const customer = customers.find(c => c.id === preSelectedCustomerId)
      if (customer) {
        handleCustomerSelect(customer)
      }
    }
  }, [preSelectedCustomerId, customers])

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      resetForm()
    }
  }, [open])

  const resetForm = () => {
    setFormData({
      client_name: '',
      service_description: '',
      amount: '',
      due_date: '',
      notes: '',
    })
    setErrors({
      client_name: '',
      service_description: '',
      amount: '',
    })
    setSelectedCustomer(null)
    setCustomerSearch('')
    setUseManualEntry(false)
    setCustomerSearchOpen(false)
  }

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer)
    setFormData(prev => ({
      ...prev,
      client_name: customer.name,
    }))
    setCustomerSearchOpen(false)
    setCustomerSearch('')
    
    // Clear client name error if it exists
    if (errors.client_name) {
      setErrors(prev => ({ ...prev, client_name: '' }))
    }
  }

  const handleManualEntry = () => {
    setUseManualEntry(true)
    setSelectedCustomer(null)
    setCustomerSearchOpen(false)
    setFormData(prev => ({ ...prev, client_name: '' }))
  }

  const handleBackToCustomerSelect = () => {
    setUseManualEntry(false)
    setFormData(prev => ({ ...prev, client_name: '' }))
  }

  const validateForm = () => {
    const newErrors = {
      client_name: '',
      service_description: '',
      amount: '',
    }

    if (!formData.client_name.trim()) {
      newErrors.client_name = 'Client name is required'
    }

    if (!formData.service_description.trim()) {
      newErrors.service_description = 'Service description is required'
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Valid amount is required'
    }

    setErrors(newErrors)
    return Object.values(newErrors).every(error => error === '')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('Please fix the errors before submitting')
      return
    }

    setLoading(true)

    try {
      const invoice = {
        customer_id: selectedCustomer?.id || null,
        client_name: formData.client_name,
        service_description: formData.service_description,
        amount: parseFloat(formData.amount),
        status: 'pending' as const,
        invoice_date: new Date().toISOString(),
        due_date: formData.due_date ? new Date(formData.due_date).toISOString() : null,
        payment_date: null,
        delivery_date: null,
        notes: formData.notes || null,
      }

      await onAdd(invoice)
      onOpenChange(false)
      toast.success('Invoice created successfully!')
    } catch (error) {
      console.error('Error adding invoice:', error)
      toast.error('Failed to create invoice')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const getCustomerDisplayInfo = (customer: Customer) => {
    const parts = []
    if (customer.company) parts.push(customer.company)
    if (customer.email) parts.push(customer.email)
    if (customer.phone) parts.push(customer.phone)
    return parts.join(' • ')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] professional-card bg-white/95 backdrop-blur-sm border-0 shadow-professional-lg">
        <DialogHeader className="text-center pb-4 border-b border-slate-200">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 rounded-2xl action-gradient flex items-center justify-center">
              <Plus className="h-6 w-6 text-white" />
            </div>
          </div>
          <DialogTitle className="text-2xl font-bold text-slate-900">
            {getTranslation(language, 'addInvoice')}
          </DialogTitle>
          <p className="text-slate-600 font-medium">
            Create a new invoice for your customer
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 max-h-[60vh] overflow-y-auto custom-scrollbar">
          {/* Customer Selection Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="form-label text-base font-semibold">
                Select Customer
              </Label>
              {!useManualEntry && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleManualEntry}
                  className="text-sm border-slate-200 hover:bg-slate-50"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Manual Entry
                </Button>
              )}
            </div>

            {!useManualEntry ? (
              <div className="space-y-3">
                {/* Customer Search/Select */}
                <div className="relative">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setCustomerSearchOpen(!customerSearchOpen)}
                    className={`w-full h-12 justify-between text-left font-normal ${
                      selectedCustomer ? 'border-emerald-200 bg-emerald-50' : 'border-slate-200'
                    }`}
                  >
                    {selectedCustomer ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 rounded-lg success-gradient flex items-center justify-center text-white text-sm font-bold">
                          {selectedCustomer.name.charAt(0).toUpperCase()}
                        </div>
                        <div className="text-left">
                          <div className="font-semibold text-slate-900">{selectedCustomer.name}</div>
                          {selectedCustomer.company && (
                            <div className="text-xs text-slate-600">{selectedCustomer.company}</div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2 text-slate-500">
                        <Search className="h-4 w-4" />
                        <span>Search and select a customer...</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      {selectedCustomer && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            setSelectedCustomer(null)
                            setFormData(prev => ({ ...prev, client_name: '' }))
                          }}
                          className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                      <Badge variant="secondary" className="text-xs">
                        {customers.length} customers
                      </Badge>
                    </div>
                  </Button>

                  {/* Customer Dropdown */}
                  {customerSearchOpen && (
                    <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-slate-200 rounded-xl shadow-lg max-h-80 overflow-hidden">
                      {/* Search Input */}
                      <div className="p-3 border-b border-slate-200">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            placeholder="Search customers..."
                            value={customerSearch}
                            onChange={(e) => setCustomerSearch(e.target.value)}
                            className="pl-10 h-10 border-slate-200 focus:border-blue-500"
                            autoFocus
                          />
                        </div>
                      </div>

                      {/* Customer List */}
                      <div className="max-h-60 overflow-y-auto custom-scrollbar">
                        {customersLoading ? (
                          <div className="p-4 text-center">
                            <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                            <p className="text-sm text-slate-600">Loading customers...</p>
                          </div>
                        ) : filteredCustomers.length > 0 ? (
                          <div className="p-2">
                            {filteredCustomers.map((customer) => (
                              <button
                                key={customer.id}
                                type="button"
                                onClick={() => handleCustomerSelect(customer)}
                                className="w-full p-3 text-left hover:bg-slate-50 rounded-lg transition-colors group"
                              >
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 rounded-xl success-gradient flex items-center justify-center text-white font-bold">
                                    {customer.name.charAt(0).toUpperCase()}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="font-semibold text-slate-900 group-hover:text-blue-600 transition-colors">
                                      {customer.name}
                                    </div>
                                    {customer.company && (
                                      <div className="flex items-center space-x-1 text-sm text-slate-600">
                                        <Building2 className="h-3 w-3" />
                                        <span>{customer.company}</span>
                                      </div>
                                    )}
                                    <div className="flex items-center space-x-3 text-xs text-slate-500 mt-1">
                                      {customer.email && (
                                        <div className="flex items-center space-x-1">
                                          <Mail className="h-3 w-3" />
                                          <span>{customer.email}</span>
                                        </div>
                                      )}
                                      {customer.phone && (
                                        <div className="flex items-center space-x-1">
                                          <Phone className="h-3 w-3" />
                                          <span>{customer.phone}</span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  <Check className="h-4 w-4 text-emerald-600 opacity-0 group-hover:opacity-100 transition-opacity" />
                                </div>
                              </button>
                            ))}
                          </div>
                        ) : (
                          <div className="p-4 text-center">
                            <User className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                            <p className="text-sm text-slate-600 font-medium">
                              {customerSearch ? 'No customers found' : 'No customers available'}
                            </p>
                            {customerSearch && (
                              <p className="text-xs text-slate-500 mt-1">
                                Try adjusting your search terms
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Selected Customer Info */}
                {selectedCustomer && (
                  <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-bold text-emerald-900 mb-2">Selected Customer</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-emerald-600" />
                            <span className="font-semibold text-emerald-800">{selectedCustomer.name}</span>
                          </div>
                          {selectedCustomer.company && (
                            <div className="flex items-center space-x-2">
                              <Building2 className="h-4 w-4 text-emerald-600" />
                              <span className="text-emerald-700">{selectedCustomer.company}</span>
                            </div>
                          )}
                          {selectedCustomer.email && (
                            <div className="flex items-center space-x-2">
                              <Mail className="h-4 w-4 text-emerald-600" />
                              <span className="text-emerald-700">{selectedCustomer.email}</span>
                            </div>
                          )}
                          {selectedCustomer.phone && (
                            <div className="flex items-center space-x-2">
                              <Phone className="h-4 w-4 text-emerald-600" />
                              <span className="text-emerald-700">{selectedCustomer.phone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleBackToCustomerSelect}
                        className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-100"
                      >
                        Change
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              /* Manual Entry Mode */
              <div className="space-y-3">
                <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-xl p-3">
                  <div className="flex items-center space-x-2">
                    <UserPlus className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">Manual Entry Mode</span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleBackToCustomerSelect}
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-100 text-sm"
                  >
                    Back to Customer List
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="client_name" className="form-label flex items-center">
                    <User className="h-4 w-4 mr-2 text-slate-500" />
                    {getTranslation(language, 'client')}
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="client_name"
                    value={formData.client_name}
                    onChange={(e) => handleInputChange('client_name', e.target.value)}
                    className={`h-12 form-input text-base ${
                      errors.client_name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                    }`}
                    placeholder="Enter client name"
                    required
                  />
                  {errors.client_name && (
                    <div className="flex items-center space-x-1 text-sm text-red-600 font-medium">
                      <AlertCircle className="h-4 w-4 flex-shrink-0" />
                      <span>{errors.client_name}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Service Description */}
          <div className="space-y-2">
            <Label htmlFor="service_description" className="form-label flex items-center">
              <Briefcase className="h-4 w-4 mr-2 text-slate-500" />
              {getTranslation(language, 'service')}
              <span className="text-red-500 ml-1">*</span>
            </Label>
            <Input
              id="service_description"
              value={formData.service_description}
              onChange={(e) => handleInputChange('service_description', e.target.value)}
              className={`h-12 form-input text-base ${
                errors.service_description ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              placeholder="Describe the service provided"
              required
            />
            {errors.service_description && (
              <div className="flex items-center space-x-1 text-sm text-red-600 font-medium">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{errors.service_description}</span>
              </div>
            )}
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount" className="form-label flex items-center">
              <DollarSign className="h-4 w-4 mr-2 text-slate-500" />
              {getTranslation(language, 'amount')} (₹)
              <span className="text-red-500 ml-1">*</span>
            </Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              className={`h-12 form-input text-base ${
                errors.amount ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              placeholder="0.00"
              required
            />
            {errors.amount && (
              <div className="flex items-center space-x-1 text-sm text-red-600 font-medium">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{errors.amount}</span>
              </div>
            )}
          </div>

          {/* Due Date */}
          <div className="space-y-2">
            <Label htmlFor="due_date" className="form-label flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-slate-500" />
              {getTranslation(language, 'dueDate')}
            </Label>
            <Input
              id="due_date"
              type="date"
              value={formData.due_date}
              onChange={(e) => handleInputChange('due_date', e.target.value)}
              className="h-12 form-input text-base"
              min={new Date().toISOString().split('T')[0]}
            />
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="form-label flex items-center">
              <FileText className="h-4 w-4 mr-2 text-slate-500" />
              {getTranslation(language, 'notes')}
            </Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              rows={3}
              className="form-input resize-none text-base"
              placeholder="Additional notes (optional)"
            />
          </div>
        </form>

        {/* Action Buttons */}
        <div className="flex space-x-3 pt-4 border-t border-slate-200">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => onOpenChange(false)} 
            className="flex-1 h-12 border-slate-200 hover:bg-slate-50 font-medium"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={loading} 
            className="btn-primary-action flex-1 h-12"
          >
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span className="font-bold">{getTranslation(language, 'loading')}</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span className="font-bold">{getTranslation(language, 'addInvoice')}</span>
              </div>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}