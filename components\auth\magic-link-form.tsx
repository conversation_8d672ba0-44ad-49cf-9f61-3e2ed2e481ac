"use client";

import { useFormContext } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Mail, Sparkles, ArrowRight, Loader2, AlertCircle } from "lucide-react";
import { useLanguage } from "@/contexts/language-context";
import { getTranslation } from "@/lib/i18n";
import { useAuth } from "@/hooks/use-auth";
import { z } from "zod";

interface MagicLinkFormProps {
  // onSubmit: (values: { email: string }) => void // This prop is no longer needed
}

const magicLinkSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
});

type MagicLinkFormData = z.infer<typeof magicLinkSchema>;

export function MagicLinkForm({}: /* onSubmit */ MagicLinkFormProps) {
  const { language } = useLanguage();
  const { loading } = useAuth();
  const {
    register,
    formState: { errors },
  } = useFormContext<MagicLinkFormData>();

  return (
    <>
      <div className="space-y-2">
        <Label
          htmlFor="email-magic"
          className="form-label flex items-center text-base"
        >
          <Mail className="h-4 w-4 mr-2 text-slate-500" aria-hidden="true" />
          Email Address
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        </Label>
        <Input
          id="email-magic"
          type="email"
          inputMode="email"
          autoComplete="email"
          placeholder="Enter your email"
          {...register("email")}
          className={`h-14 text-base border-2 rounded-2xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
            errors.email
              ? "border-red-300 focus:border-red-500 focus:ring-red-500"
              : "form-input"
          }`}
          style={{ fontSize: "16px" }}
          required
          aria-invalid={errors.email ? "true" : "false"}
          aria-describedby={
            errors.email ? "email-magic-error" : "email-magic-help"
          }
        />
        {errors.email && (
          <div
            id="email-magic-error"
            className="flex items-center space-x-1 text-sm text-red-600 font-medium"
            role="alert"
          >
            <AlertCircle className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
            <span>{errors.email.message}</span>
          </div>
        )}
        <p id="email-magic-help" className="sr-only">
          Enter your email address to receive a secure login link
        </p>
      </div>

      <div
        className="bg-blue-50 border border-blue-200 rounded-2xl p-4"
        role="note"
      >
        <div className="flex items-start space-x-2">
          <Sparkles
            className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"
            aria-hidden="true"
          />
          <p className="text-blue-800 text-sm font-medium">
            {getTranslation(language, "magicLinkDescription")}
          </p>
        </div>
      </div>
    </>
  );
}
