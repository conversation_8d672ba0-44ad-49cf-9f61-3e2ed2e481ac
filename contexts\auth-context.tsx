'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  profile: any | null // Add profile to the context type
  loading: boolean
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<any | null>(null) // State for profile
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session and profile
    const getInitialSessionAndProfile = async () => {
      try {
        const response = await fetch('/api/auth/user')
        if (response.ok) {
          const { user, profile } = await response.json()
          setUser(user ?? null)
          setProfile(profile ?? null) // Set profile from API response
        }
      } catch (error) {
        console.error('Error fetching user and profile:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSessionAndProfile()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setUser(session?.user ?? null)
        // For simplicity, profile is not updated here. A full solution might re-fetch profile or use real-time.
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signOut = async () => {
    try {
      const response = await fetch('/api/auth/signout', { method: 'POST' })
      if (!response.ok) {
        throw new Error('Sign out failed')
      }
      setUser(null) // Clear user on sign out
      setProfile(null) // Clear profile on sign out
      router.push('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return (
    <AuthContext.Provider value={{ user, profile, loading, signOut }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
