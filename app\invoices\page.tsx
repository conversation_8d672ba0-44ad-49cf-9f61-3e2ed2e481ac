'use client'

import { Suspense } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { PhoneAuth } from '@/components/auth/phone-auth'
import { InvoicesContent } from '@/components/invoices/invoices-content'
import ErrorBoundary from '@/components/error-boundary'

export default function InvoicesPage() {
  const { user, loading: authLoading } = useAuth()

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return <PhoneAuth />
  }

  return (
    <ErrorBoundary>
      <Suspense fallback={<div>Loading invoices...</div>}>
        <InvoicesContent />
      </Suspense>
    </ErrorBoundary>
  )
}
