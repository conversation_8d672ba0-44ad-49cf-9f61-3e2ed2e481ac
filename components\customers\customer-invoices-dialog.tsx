'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { useInvoices, Invoice } from '@/hooks/use-invoices'
import { Customer } from '@/hooks/use-customers'
import { 
  FileText, 
  Search, 
  SortAsc, 
  SortDesc, 
  Calendar, 
  DollarSign,
  Clock,
  CheckCircle,
  Package,
  Plus,
  X,
  AlertCircle,
  Loader2,
  RefreshCw
} from 'lucide-react'
import { format } from 'date-fns'
import { toast } from 'sonner'

interface CustomerInvoicesDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  customer: Customer | null
  onCreateInvoice?: (customerId: string) => void
}

export function CustomerInvoicesDialog({ 
  open, 
  onOpenChange, 
  customer,
  onCreateInvoice 
}: CustomerInvoicesDialogProps) {
  const { language } = useLanguage()
  const { fetchCustomerInvoices, updateInvoiceStatus } = useInvoices()
  
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'paid' | 'pending' | 'delivered'>('all')
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'status'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [error, setError] = useState<string | null>(null)

  // Fetch customer invoices when dialog opens
  useEffect(() => {
    if (open && customer) {
      loadCustomerInvoices()
    } else {
      // Reset state when dialog closes
      setInvoices([])
      setError(null)
      setSearchQuery('')
      setStatusFilter('all')
    }
  }, [open, customer])

  const loadCustomerInvoices = async (showRefreshIndicator = false) => {
    if (!customer) return
    
    if (showRefreshIndicator) {
      setRefreshing(true)
    } else {
      setLoading(true)
    }
    
    setError(null)
    
    try {
      console.log('Loading invoices for customer:', customer.id, customer.name)
      const customerInvoices = await fetchCustomerInvoices(customer.id)
      console.log('Loaded customer invoices:', customerInvoices)
      setInvoices(customerInvoices)
      
      if (customerInvoices.length === 0) {
        console.log('No invoices found for customer:', customer.name)
      }
    } catch (error) {
      console.error('Error loading customer invoices:', error)
      setError('Failed to load invoices. Please try again.')
      toast.error('Failed to load customer invoices')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = () => {
    loadCustomerInvoices(true)
  }

  // Filter and sort invoices
  const filteredInvoices = invoices
    .filter(invoice => {
      const matchesSearch = searchQuery === '' || 
        invoice.service_description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (invoice.invoice_number && invoice.invoice_number.toLowerCase().includes(searchQuery.toLowerCase()))
      
      const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter
      
      return matchesSearch && matchesStatus
    })
    .sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          break
        case 'amount':
          comparison = a.amount - b.amount
          break
        case 'status':
          comparison = a.status.localeCompare(b.status)
          break
      }
      
      return sortOrder === 'asc' ? comparison : -comparison
    })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'delivered':
        return <Package className="h-4 w-4" />
      default:
        return null
    }
  }

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'paid':
        return 'status-paid border font-medium'
      case 'pending':
        return 'status-pending border font-medium'
      case 'delivered':
        return 'status-delivered border font-medium'
      default:
        return 'bg-slate-100 text-slate-800 border border-slate-200 font-medium'
    }
  }

  const handleStatusUpdate = async (invoiceId: string, newStatus: 'paid' | 'delivered') => {
    try {
      await updateInvoiceStatus(invoiceId, newStatus)
      // Refresh the invoices list to show updated status
      await loadCustomerInvoices(true)
    } catch (error) {
      console.error('Error updating invoice status:', error)
    }
  }

  const handleCreateInvoice = () => {
    if (customer && onCreateInvoice) {
      onCreateInvoice(customer.id)
      onOpenChange(false)
    }
  }

  const clearFilters = () => {
    setSearchQuery('')
    setStatusFilter('all')
  }

  const hasActiveFilters = searchQuery !== '' || statusFilter !== 'all'

  if (!customer) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] professional-card bg-white/95 backdrop-blur-sm border-0 shadow-professional-lg">
        <DialogHeader className="text-center pb-4 border-b border-slate-200">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 rounded-2xl action-gradient flex items-center justify-center">
              <FileText className="h-6 w-6 text-white" />
            </div>
          </div>
          <DialogTitle className="text-2xl font-bold text-slate-900">
            {customer.name} - Invoices
          </DialogTitle>
          <p className="text-slate-600 font-medium">
            {customer.company && `${customer.company} • `}
            {customer.email || customer.phone}
          </p>
          
          {/* Debug Info - Remove in production */}
          <div className="text-xs text-slate-500 bg-slate-50 p-2 rounded-lg mt-2">
            <strong>Debug Info:</strong> Customer ID: {customer.id}
          </div>
        </DialogHeader>

        <div className="space-y-4 max-h-[60vh] overflow-y-auto custom-scrollbar">
          {/* Search and Filters */}
          <div className="space-y-3">
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search invoices..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-10 form-input text-sm bg-white/80 backdrop-blur-sm"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchQuery('')}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
              
              <Button
                onClick={handleRefresh}
                disabled={refreshing}
                variant="outline"
                size="sm"
                className="h-10 px-3 border-slate-200 hover:bg-slate-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              
              <Button
                onClick={handleCreateInvoice}
                className="btn-primary-action h-10 px-4 whitespace-nowrap text-sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Invoice
              </Button>
            </div>

            <div className="flex flex-wrap gap-3 items-center justify-between">
              <div className="flex flex-wrap gap-3 items-center">
                {/* Status Filter */}
                <Select value={statusFilter} onValueChange={value => setStatusFilter(value as "paid" | "pending" | "delivered" | "all")}>
                  <SelectTrigger className="w-auto min-w-[120px] h-8 bg-white/80 backdrop-blur-sm border-slate-200 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                        <span>{getTranslation(language, 'pending')}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="paid">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                        <span>{getTranslation(language, 'paid')}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="delivered">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        <span>{getTranslation(language, 'delivered')}</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>

                {/* Sort Options */}
                <Select value={sortBy} onValueChange={value => setSortBy(value as 'date' | 'amount' | 'status')}>
                  <SelectTrigger className="w-auto min-w-[100px] h-8 bg-white/80 backdrop-blur-sm border-slate-200 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="amount">Amount</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="h-8 bg-white/80 backdrop-blur-sm border-slate-200 hover:bg-white"
                >
                  {sortOrder === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />}
                </Button>

                {/* Clear Filters */}
                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="h-8 text-slate-500 hover:text-slate-700 text-sm"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Clear
                  </Button>
                )}
              </div>

              {/* Results Count */}
              <Badge variant="secondary" className="bg-slate-100 text-slate-700 font-medium text-xs">
                {filteredInvoices.length} {filteredInvoices.length === 1 ? 'invoice' : 'invoices'}
              </Badge>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
                <div>
                  <p className="text-red-800 font-medium">{error}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadCustomerInvoices()}
                    className="mt-2 border-red-200 hover:bg-red-50"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-3" />
                <p className="text-slate-600 font-medium">Loading invoices...</p>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!loading && !error && filteredInvoices.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 rounded-2xl bg-slate-100 flex items-center justify-center mx-auto mb-4">
                {searchQuery || statusFilter !== 'all' ? (
                  <Search className="h-8 w-8 text-slate-400" />
                ) : (
                  <FileText className="h-8 w-8 text-slate-400" />
                )}
              </div>
              
              {searchQuery || statusFilter !== 'all' ? (
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">No invoices found</h3>
                  <p className="text-slate-600 font-medium mb-4">
                    Try adjusting your search or filter criteria
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center text-sm text-slate-500">
                    {searchQuery && (
                      <span className="bg-slate-100 px-3 py-1 rounded-full">
                        Search: "{searchQuery}"
                      </span>
                    )}
                    {statusFilter !== 'all' && (
                      <span className="bg-slate-100 px-3 py-1 rounded-full">
                        Status: {getTranslation(language, statusFilter as any)}
                      </span>
                    )}
                  </div>
                </div>
              ) : (
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">
                    No invoices yet
                  </h3>
                  <p className="text-slate-600 font-medium mb-4">
                    Create the first invoice for {customer.name}
                  </p>
                  <Button
                    onClick={handleCreateInvoice}
                    className="btn-primary-action"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Invoices List */}
          {!loading && !error && filteredInvoices.length > 0 && (
            <div className="space-y-3">
              {filteredInvoices.map((invoice) => (
                <Card key={invoice.id} className="professional-card border-0 shadow-sm bg-white/90 backdrop-blur-sm hover:shadow-md transition-all duration-200">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-bold text-base text-slate-900">
                            {invoice.service_description}
                          </h4>
                          {invoice.invoice_number && (
                            <Badge variant="outline" className="text-xs font-medium">
                              {invoice.invoice_number}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-slate-600">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{format(new Date(invoice.invoice_date), 'MMM dd, yyyy')}</span>
                          </div>
                          {invoice.due_date && (
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>Due: {format(new Date(invoice.due_date), 'MMM dd')}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="amount-display text-xl font-bold text-slate-900 mb-1">
                          ₹{invoice.amount.toLocaleString()}
                        </div>
                        <Badge className={`${getStatusStyle(invoice.status)} text-xs`}>
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(invoice.status)}
                            <span>{getTranslation(language, invoice.status)}</span>
                          </div>
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  {(invoice.notes || invoice.status === 'pending' || invoice.status === 'paid') && (
                    <CardContent className="pt-0">
                      {invoice.notes && (
                        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200 mb-3">
                          <p className="text-sm text-blue-800 font-medium">
                            {invoice.notes}
                          </p>
                        </div>
                      )}

                      {invoice.status === 'pending' && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleStatusUpdate(invoice.id, 'paid')}
                            className="btn-success-action flex-1 text-sm"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Mark Paid
                          </Button>
                        </div>
                      )}

                      {invoice.status === 'paid' && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleStatusUpdate(invoice.id, 'delivered')}
                            className="btn-primary-action flex-1 text-sm"
                          >
                            <Package className="h-3 w-3 mr-1" />
                            Mark Delivered
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Summary Footer */}
        {!loading && !error && filteredInvoices.length > 0 && (
          <div className="border-t border-slate-200 pt-4 mt-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-emerald-600">
                  ₹{filteredInvoices.filter(i => i.status === 'paid').reduce((sum, i) => sum + i.amount, 0).toLocaleString()}
                </div>
                <div className="text-xs text-slate-500 font-medium">Total Paid</div>
              </div>
              <div>
                <div className="text-lg font-bold text-amber-600">
                  ₹{filteredInvoices.filter(i => i.status === 'pending').reduce((sum, i) => sum + i.amount, 0).toLocaleString()}
                </div>
                <div className="text-xs text-slate-500 font-medium">Total Pending</div>
              </div>
              <div>
                <div className="text-lg font-bold text-slate-700">
                  {filteredInvoices.filter(i => i.status === 'delivered').length}
                </div>
                <div className="text-xs text-slate-500 font-medium">Delivered</div>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}