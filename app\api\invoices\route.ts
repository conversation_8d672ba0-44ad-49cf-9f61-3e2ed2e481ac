import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Generate invoice number based on date and sequence
const generateInvoiceNumber = (date: Date, sequence: number): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const seq = String(sequence).padStart(4, '0')
  return `INV-${year}${month}${day}-${seq}`
}

export async function GET(request: Request) {
  const cookieStore = cookies()
  const supabaseServer = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )

  const { data: { user }, error: userError } = await supabaseServer.auth.getUser()

  if (userError) {
    console.error('Supabase getUser error:', userError)
    return NextResponse.json({ error: userError.message }, { status: 401 })
  }

  if (!user) {
    console.log('No user found for /api/invoices GET request. Returning 401.')
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const { searchParams } = new URL(request.url)
  const customerId = searchParams.get('customerId')

  let query = supabase
    .from('invoices')
    .select('*')
    .eq('user_id', user.id)

  if (customerId) {
    query = query.eq('customer_id', customerId)
  }

  const { data, error } = await query.order('created_at', { ascending: false })

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  const invoicesWithNumbers = data?.map((invoice, index) => ({
    ...invoice,
    invoice_number: (invoice as any).invoice_number || generateInvoiceNumber(new Date(invoice.created_at), data.length - index)
  })) || []

  return NextResponse.json({ data: invoicesWithNumbers })
}

export async function POST(request: Request) {
  const cookieStore = cookies()
  const supabaseServer = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )

  const { data: { user }, error: userError } = await supabaseServer.auth.getUser()

  if (userError) {
    console.error('Supabase getUser error:', userError)
    return NextResponse.json({ error: userError.message }, { status: 401 })
  }

  if (!user) {
    console.log('No user found for /api/invoices POST request. Returning 401.')
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const invoice = await request.json()

  const { count, error: countError } = await supabase
    .from('invoices')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)

  if (countError) {
    return NextResponse.json({ error: countError.message }, { status: 500 })
  }

  const { data, error } = await supabase
    .from('invoices')
    .insert([{ ...invoice, user_id: user.id }])
    .select()
    .single()

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  const sequence = (count || 0) + 1
  const invoiceNumber = generateInvoiceNumber(new Date(data.created_at), sequence)

  const invoiceWithNumber = {
    ...data,
    invoice_number: invoiceNumber
  }

  return NextResponse.json({ data: invoiceWithNumber })
}