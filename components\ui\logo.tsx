'use client'

import Image from 'next/image'
import { cn } from '@/lib/utils'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'full' | 'icon-only' | 'text-only'
  className?: string
  showTagline?: boolean
  onClick?: () => void
}

const sizeConfig = {
  sm: { pixels: 40, textSize: 'text-lg', taglineSize: 'text-xs' },
  md: { pixels: 60, textSize: 'text-xl', taglineSize: 'text-sm' },
  lg: { pixels: 80, textSize: 'text-2xl', taglineSize: 'text-base' }
}

export function Logo({
  size = 'md',
  variant = 'full',
  className,
  showTagline = false,
  onClick
}: LogoProps) {
  const config = sizeConfig[size]
  const containerSize = `w-[${config.pixels}px] h-[${config.pixels}px]`

  const IconComponent = () => (
    <div
      className={cn(
        containerSize,
        'rounded-2xl brand-gradient flex items-center justify-center shadow-lg',
        onClick && 'cursor-pointer hover:scale-105 transition-transform duration-200'
      )}
      role="img"
      aria-label="InstraBillBoi logo"
      onClick={onClick}
    >
      <Image
        src="/logo.png"
        alt="InstraBillBoi logo"
        width={config.pixels * 0.7}
        height={config.pixels * 0.7}
        className="object-contain"
      />
    </div>
  )

  const TextComponent = () => (
    <div className="flex flex-col">
      <h1 className={cn(config.textSize, 'font-bold text-slate-900 leading-tight')}>
        InstraBillBoi
      </h1>
      {showTagline && (
        <p className={cn(config.taglineSize, 'text-slate-600 font-medium -mt-1')}>
          Professional Invoice Management
        </p>
      )}
    </div>
  )

  if (variant === 'icon-only') {
    return (
      <div className={cn('inline-flex', className)}>
        <IconComponent />
      </div>
    )
  }

  if (variant === 'text-only') {
    return (
      <div className={cn('inline-flex', className)}>
        <TextComponent />
      </div>
    )
  }

  return (
    <div className={cn('inline-flex items-center space-x-3', className)}>
      <IconComponent />
      <TextComponent />
    </div>
  )
}

// Specialized logo component for authentication pages
export function AuthLogo({ className }: { className?: string }) {
  const logoSize = 80

  return (
    <header className={cn('text-center mb-8', className)}>
      <div className="inline-flex items-center justify-center w-20 h-20 rounded-3xl bg-white shadow-professional border border-slate-200 mb-6" role="img" aria-label="InstraBillBoi logo">
        <div className="w-[72px] h-[72px] rounded-2xl brand-gradient flex items-center justify-center">
          <Image
            src="/logo.png"
            alt="InstraBillBoi logo"
            width={logoSize * 0.7}
            height={logoSize * 0.7}
            className="object-contain"
          />
        </div>
      </div>
      <h1 className="text-3xl font-bold text-slate-900 mb-2">InstraBillBoi</h1>
      <p className="text-slate-600 font-medium">Professional Invoice Management</p>
    </header>
  )
}
