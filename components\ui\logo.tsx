'use client'

import Image from 'next/image'
import { cn } from '@/lib/utils'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'full' | 'icon-only' | 'text-only'
  className?: string
  showTagline?: boolean
  onClick?: () => void
}

const sizeConfig = {
  sm: {
    container: 'w-8 h-8',
    icon: 'h-4 w-4',
    title: 'text-lg',
    tagline: 'text-xs'
  },
  md: {
    container: 'w-12 h-12',
    icon: 'h-6 w-6',
    title: 'text-xl',
    tagline: 'text-sm'
  },
  lg: {
    container: 'w-16 h-16',
    icon: 'h-8 w-8',
    title: 'text-2xl',
    tagline: 'text-base'
  },
  xl: {
    container: 'w-20 h-20',
    icon: 'h-10 w-10',
    title: 'text-3xl',
    tagline: 'text-lg'
  }
}

export function Logo({ 
  size = 'md', 
  variant = 'full', 
  className,
  showTagline = false,
  onClick 
}: LogoProps) {
  const config = sizeConfig[size]
  
  const IconComponent = () => (
    <div 
      className={cn(
        config.container,
        'rounded-2xl brand-gradient flex items-center justify-center shadow-lg',
        onClick && 'cursor-pointer hover:scale-105 transition-transform duration-200'
      )}
      role="img" 
      aria-label="InstraBillBoi logo"
      onClick={onClick}
    >
      <Image
        src="/logo.png"
        alt="InstraBillBoi logo"
        width={config.icon.includes('h-8') ? 40 : config.icon.includes('h-6') ? 24 : config.icon.includes('h-8') ? 32 : 40}
        height={config.icon.includes('h-8') ? 40 : config.icon.includes('h-6') ? 24 : config.icon.includes('h-8') ? 32 : 40}
        className={cn(config.icon, 'object-contain')}
      />
    </div>
  )

  const TextComponent = () => (
    <div className="flex flex-col">
      <h1 className={cn(config.title, 'font-bold text-slate-900 leading-tight')}>
        InstraBillBoi
      </h1>
      {showTagline && (
        <p className={cn(config.tagline, 'text-slate-600 font-medium -mt-1')}>
          Professional Invoice Management
        </p>
      )}
    </div>
  )

  if (variant === 'icon-only') {
    return (
      <div className={cn('inline-flex', className)}>
        <IconComponent />
      </div>
    )
  }

  if (variant === 'text-only') {
    return (
      <div className={cn('inline-flex', className)}>
        <TextComponent />
      </div>
    )
  }

  return (
    <div className={cn('inline-flex items-center space-x-3', className)}>
      <IconComponent />
      <TextComponent />
    </div>
  )
}

// Specialized logo component for authentication pages
export function AuthLogo({ className }: { className?: string }) {
  return (
    <header className={cn('text-center mb-8', className)}>
      <div className="inline-flex items-center justify-center w-20 h-20 rounded-3xl bg-white shadow-professional border border-slate-200 mb-6" role="img" aria-label="InstraBillBoi logo">
        <div className="w-18 h-18 rounded-2xl brand-gradient flex items-center justify-center">
        <Image
          src="/logo.png"
          alt="InstraBillBoi logo"
          width={80}
          height={80}
          className="object-contain"
        />
      </div>
      </div>
      <h1 className="text-3xl font-bold text-slate-900 mb-2">InstraBillBoi</h1>
      <p className="text-slate-600 font-medium">Professional Invoice Management</p>
    </header>
  )
}
