import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  const cookieStore = cookies()
  const supabaseServer = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )

  const { data: { user }, error: userError } = await supabaseServer.auth.getUser()

  if (userError) {
    console.error('Supabase getUser error:', userError)
    return NextResponse.json({ error: userError.message }, { status: 401 })
  }

  if (!user) {
    console.log('No user found for /api/customers GET request. Returning 401.')
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({ data })
}

export async function POST(request: Request) {
  const cookieStore = cookies()
  const supabaseServer = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )

  const { data: { user }, error: userError } = await supabaseServer.auth.getUser()

  if (userError) {
    console.error('Supabase getUser error:', userError)
    return NextResponse.json({ error: userError.message }, { status: 401 })
  }

  if (!user) {
    console.log('No user found for /api/customers POST request. Returning 401.')
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const customer = await request.json()

  const { data, error } = await supabase
    .from('customers')
    .insert([{ ...customer, user_id: user.id }])
    .select()
    .single()

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({ data })
}