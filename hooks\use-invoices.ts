'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { toast } from 'sonner'

export interface Invoice {
  id: string
  user_id: string
  customer_id: string | null
  client_name: string
  service_description: string
  amount: number
  status: 'paid' | 'pending' | 'delivered'
  invoice_date: string
  due_date: string | null
  payment_date: string | null
  delivery_date: string | null
  notes: string | null
  created_at: string
  updated_at: string
  invoice_number?: string
}

export function useInvoices() {
  const { user } = useAuth()
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)

  const fetchInvoices = async () => {
    if (!user) return

    try {
      const response = await fetch('/api/invoices', { credentials: 'include' })
      if (!response.ok) throw new Error('Failed to fetch')
      const { data } = await response.json()
      setInvoices(data || [])
    } catch (error) {
      console.error('Error fetching invoices:', error)
      toast.error('Failed to fetch invoices')
    } finally {
      setLoading(false)
    }
  }

  const fetchCustomerInvoices = async (customerId: string) => {
    if (!user) return []

    try {
      const response = await fetch(`/api/invoices?customerId=${customerId}`, { credentials: 'include' })
      if (!response.ok) throw new Error('Failed to fetch')
      const { data } = await response.json()
      return data || []
    } catch (error) {
      console.error('Error fetching customer invoices:', error)
      toast.error('Failed to fetch customer invoices')
      return []
    }
  }

  const updateInvoiceStatus = async (id: string, status: 'paid' | 'delivered') => {
    try {
      const updateData: any = { status }
      if (status === 'paid') {
        updateData.payment_date = new Date().toISOString()
      } else if (status === 'delivered') {
        updateData.delivery_date = new Date().toISOString()
      }

      const response = await fetch(`/api/invoices/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData),
        credentials: 'include',
      })

      if (!response.ok) {
        const { error } = await response.json()
        throw new Error(error)
      }

      const { data } = await response.json()
      setInvoices(prev => prev.map(inv =>
        inv.id === id ? { ...inv, ...data } : inv
      ))

      toast.success(`Invoice marked as ${status}`)
    } catch (error) {
      console.error('Error updating invoice:', error)
      toast.error('Failed to update invoice')
    }
  }

  const addInvoice = async (invoice: Omit<Invoice, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'invoice_number'>) => {
    if (!user) return

    try {
      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoice),
        credentials: 'include',
      })

      if (!response.ok) {
        const { error } = await response.json()
        throw new Error(error)
      }

      const { data } = await response.json()
      setInvoices(prev => [data, ...prev])
      toast.success('Invoice added successfully')
      return data
    } catch (error) {
      console.error('Error adding invoice:', error)
      toast.error('Failed to add invoice')
      throw error
    }
  }

  useEffect(() => {
    fetchInvoices()
  }, [user])

  const paidInvoices = invoices.filter(inv => inv.status === 'paid')
  const pendingInvoices = invoices.filter(inv => inv.status === 'pending')
  const deliveredInvoices = invoices.filter(inv => inv.status === 'delivered')

  const totalPaid = paidInvoices.reduce((sum, inv) => sum + inv.amount, 0)
  const totalPending = pendingInvoices.reduce((sum, inv) => sum + inv.amount, 0)

  return {
    invoices,
    paidInvoices,
    pendingInvoices,
    deliveredInvoices,
    totalPaid,
    totalPending,
    loading,
    updateInvoiceStatus,
    addInvoice,
    refreshInvoices: fetchInvoices,
    fetchCustomerInvoices,
  }
}
