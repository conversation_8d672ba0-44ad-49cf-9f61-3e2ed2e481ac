'use client'

import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Mail, KeyRound, ArrowRight, Loader2, AlertCircle } from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'
import * as z from 'zod'

interface ForgotPasswordFormProps {
  onBackToSignIn: () => void
}

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
})

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

export function ForgotPasswordForm({ onBackToSignIn }: ForgotPasswordFormProps) {
  const { loading } = useAuth()
  const { register, formState: { errors } } = useFormContext<ForgotPasswordFormData>()

  return (
    <>
      <div className="space-y-4">
        <Label htmlFor="forgot-email" className="form-label flex items-center text-base">
          <Mail className="h-4 w-4 mr-2 text-slate-500" aria-hidden="true" />
          Email Address
          <span className="text-red-500 ml-1" aria-label="required">*</span>
        </Label>
        <Input
          id="forgot-email"
          type="email"
          inputMode="email"
          autoComplete="email"
          placeholder="Enter your email"
          {...register('email')}
          className={`h-14 text-base border-2 rounded-2xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
            errors.email
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'form-input'
          }`}
          style={{ fontSize: '16px' }}
          required
          aria-invalid={errors.email ? 'true' : 'false'}
          aria-describedby={errors.email ? 'forgot-email-error' : undefined}
        />
        {errors.email && (
          <div id="forgot-email-error" className="flex items-center space-x-1 text-sm text-red-600 font-medium" role="alert">
            <AlertCircle className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
            <span>{errors.email.message}</span>
          </div>
        )}
      </div>

      <Button
        type="button"
        disabled={loading}
        className="w-full h-14 text-base font-bold action-gradient hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-describedby="forgot-password-button-help"
      >
        {loading ? (
          <div className="flex items-center space-x-2">
            <Loader2 className="h-5 w-5 animate-spin" aria-hidden="true" />
            <span>Sending Reset Link...</span>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <KeyRound className="h-5 w-5" aria-hidden="true" />
            <span>Send Reset Link</span>
            <ArrowRight className="h-5 w-5" aria-hidden="true" />
          </div>
        )}
      </Button>
      <p id="forgot-password-button-help" className="sr-only">
        Click to send a password reset link to your email address
      </p>

      <Button
        type="button"
        variant="link"
        onClick={onBackToSignIn}
        className="w-full text-sm text-blue-600 hover:text-blue-700 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-1 py-1"
      >
        Back to Sign In
      </Button>
    </>
  )
}
