'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { useInvoices } from '@/hooks/use-invoices'
import { Header } from './header'
import { DashboardStats } from './dashboard-stats'
import { InvoiceCard } from './invoice-card'
import { BottomNav } from '@/components/navigation/bottom-nav'
import { FabSpeedDial } from '@/components/navigation/fab-speed-dial'
import { AddInvoiceDialog } from './add-invoice-dialog'
import { CheckCircle, Clock, Package, FileText } from 'lucide-react'

export function Dashboard() {
  const { language } = useLanguage()
  const {
    paidInvoices,
    pendingInvoices,
    deliveredInvoices,
    totalPaid,
    totalPending,
    loading,
    updateInvoiceStatus,
    addInvoice,
  } = useInvoices()
  
  const [addDialog<PERSON><PERSON>, setAddDialogOpen] = useState(false)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="text-center">
          <div className="w-16 h-16 rounded-2xl brand-gradient flex items-center justify-center mb-4 mx-auto">
            <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
          </div>
          <p className="text-slate-600 font-medium">Loading InstraBillBoi...</p>
        </div>
      </div>
    )
  }

  const totalInvoices = paidInvoices.length + pendingInvoices.length + deliveredInvoices.length

  const EmptyState = ({ icon: Icon, title }: { icon: any, title: string }) => (
    <div className="text-center py-16">
      <div className="w-20 h-20 rounded-2xl bg-slate-100 flex items-center justify-center mx-auto mb-4">
        <Icon className="h-10 w-10 text-slate-400" />
      </div>
      <p className="text-slate-600 font-medium mb-2">{getTranslation(language, 'noInvoices')}</p>
      <p className="text-slate-400 text-sm font-medium">Create your first invoice to get started</p>
    </div>
  )

  const handleAddCustomer = () => {
    // Placeholder for future customer functionality
    console.log('Add Customer functionality - Coming soon!')
    // You can add a toast notification here
    // toast.info('Customer management coming soon!')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-slate-100/30 to-blue-50/20">
      <Header />
      
      <main className="clean-layout py-6 pb-20">
        <DashboardStats
          totalPaid={totalPaid}
          totalPending={totalPending}
          totalInvoices={totalInvoices}
          totalDelivered={deliveredInvoices.length}
        />

        <Tabs defaultValue="pending" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-white/80 backdrop-blur-sm border border-slate-200 shadow-sm">
            <TabsTrigger 
              value="pending" 
              className="flex items-center space-x-2 data-[state=active]:bg-amber-50 data-[state=active]:text-amber-700 data-[state=active]:border-amber-200 font-medium"
            >
              <Clock className="h-4 w-4" />
              <span className="hidden sm:inline">{getTranslation(language, 'pending')}</span>
              <span className="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs font-bold">
                {pendingInvoices.length}
              </span>
            </TabsTrigger>
            <TabsTrigger 
              value="paid" 
              className="flex items-center space-x-2 data-[state=active]:bg-emerald-50 data-[state=active]:text-emerald-700 data-[state=active]:border-emerald-200 font-medium"
            >
              <CheckCircle className="h-4 w-4" />
              <span className="hidden sm:inline">{getTranslation(language, 'paid')}</span>
              <span className="bg-emerald-100 text-emerald-800 px-2 py-1 rounded-full text-xs font-bold">
                {paidInvoices.length}
              </span>
            </TabsTrigger>
            <TabsTrigger 
              value="delivered" 
              className="flex items-center space-x-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 font-medium"
            >
              <Package className="h-4 w-4" />
              <span className="hidden sm:inline">{getTranslation(language, 'delivered')}</span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-bold">
                {deliveredInvoices.length}
              </span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="space-y-4">
            {pendingInvoices.length === 0 ? (
              <EmptyState icon={Clock} title="No pending invoices" />
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {pendingInvoices.map((invoice) => (
                  <InvoiceCard
                    key={invoice.id}
                    invoice={invoice}
                    onUpdateStatus={updateInvoiceStatus}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="paid" className="space-y-4">
            {paidInvoices.length === 0 ? (
              <EmptyState icon={CheckCircle} title="No paid invoices" />
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {paidInvoices.map((invoice) => (
                  <InvoiceCard
                    key={invoice.id}
                    invoice={invoice}
                    onUpdateStatus={updateInvoiceStatus}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="delivered" className="space-y-4">
            {deliveredInvoices.length === 0 ? (
              <EmptyState icon={Package} title="No delivered invoices" />
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {deliveredInvoices.map((invoice) => (
                  <InvoiceCard
                    key={invoice.id}
                    invoice={invoice}
                    onUpdateStatus={updateInvoiceStatus}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>

      {/* Bottom Navigation */}
      <BottomNav />
      
      {/* Floating Action Button with Speed Dial */}
      <FabSpeedDial 
        onAddInvoice={() => setAddDialogOpen(true)}
        onAddCustomer={handleAddCustomer}
      />
      
      <AddInvoiceDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        onAdd={addInvoice}
      />
    </div>
  )
}