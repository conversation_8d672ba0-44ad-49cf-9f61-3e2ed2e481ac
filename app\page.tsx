'use client'

import { useAuth } from '@/contexts/auth-context'
import { PhoneAuth } from '@/components/auth/phone-auth'
import { Dashboard } from '@/components/dashboard/dashboard'

export default function HomePage() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return <PhoneAuth />  
  }

  return <Dashboard />
}