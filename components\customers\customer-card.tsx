'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Customer } from '@/hooks/use-customers'
import { 
  User, 
  Mail, 
  Phone, 
  Building2, 
  MapPin, 
  Edit, 
  Trash2, 
  FileText,
  Calendar,
  Plus
} from 'lucide-react'
import { format } from 'date-fns'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { CustomerInvoicesDialog } from './customer-invoices-dialog'

interface CustomerCardProps {
  customer: Customer
  onDelete: (id: string) => void
}

export function CustomerCard({ customer, onDelete }: CustomerCardProps) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)
  const [invoicesDialogOpen, setInvoicesDialogOpen] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await onDelete(customer.id)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleEdit = () => {
    router.push(`/customers/${customer.id}/edit`)
  }

  const handleCreateInvoice = (customerId?: string) => {
    // Navigate to invoice creation with customer pre-selected
    const customerParam = customerId || customer.id
    router.push(`/invoices/new?customer=${customerParam}`)
  }

  const handleViewInvoices = () => {
    setInvoicesDialogOpen(true)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatAddress = () => {
    const parts = [
      customer.address_line_1,
      customer.city,
      customer.state,
      customer.postal_code
    ].filter(Boolean)
    
    return parts.length > 0 ? parts.join(', ') : null
  }

  return (
    <>
      <Card className="professional-card card-hover border-0 shadow-professional bg-white/90 backdrop-blur-sm overflow-hidden">
        <CardHeader className="professional-card-header pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              {/* Avatar */}
              <div className="w-12 h-12 rounded-xl success-gradient flex items-center justify-center text-white font-bold text-lg">
                {getInitials(customer.name)}
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="font-bold text-lg text-slate-900 truncate">
                  {customer.name}
                </h3>
                {customer.company && (
                  <div className="flex items-center space-x-1 mt-1">
                    <Building2 className="h-3 w-3 text-slate-500" />
                    <p className="text-sm text-slate-600 font-medium truncate">
                      {customer.company}
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            <Badge variant="secondary" className="bg-emerald-100 text-emerald-700 border-emerald-200 font-medium">
              Active
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="professional-card-content space-y-3 pt-0">
          {/* Contact Information */}
          <div className="space-y-2">
            {customer.email && (
              <div className="flex items-center space-x-2 text-sm">
                <Mail className="h-4 w-4 text-slate-500 flex-shrink-0" />
                <span className="text-slate-700 font-medium truncate">{customer.email}</span>
              </div>
            )}
            
            {customer.phone && (
              <div className="flex items-center space-x-2 text-sm">
                <Phone className="h-4 w-4 text-slate-500 flex-shrink-0" />
                <span className="text-slate-700 font-medium">{customer.phone}</span>
              </div>
            )}
            
            {formatAddress() && (
              <div className="flex items-start space-x-2 text-sm">
                <MapPin className="h-4 w-4 text-slate-500 flex-shrink-0 mt-0.5" />
                <span className="text-slate-700 font-medium line-clamp-2">
                  {formatAddress()}
                </span>
              </div>
            )}
          </div>

          {/* Notes */}
          {customer.notes && (
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800 font-medium line-clamp-2">
                {customer.notes}
              </p>
            </div>
          )}

          {/* Created Date */}
          <div className="flex items-center space-x-2 text-xs text-slate-500 pt-2 border-t border-slate-100">
            <Calendar className="h-3 w-3" />
            <span>Added {format(new Date(customer.created_at), 'MMM dd, yyyy')}</span>
          </div>
        </CardContent>

        <CardFooter className="pt-0 bg-slate-50/50">
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewInvoices}
              className="flex-1 border-blue-200 hover:bg-blue-50 hover:border-blue-400 font-medium"
            >
              <FileText className="h-4 w-4 mr-2" />
              Invoices
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleCreateInvoice()}
              className="border-emerald-200 hover:bg-emerald-50 hover:border-emerald-400 font-medium"
            >
              <Plus className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleEdit}
              className="border-slate-200 hover:bg-slate-50 hover:border-slate-400 font-medium"
            >
              <Edit className="h-4 w-4" />
            </Button>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-red-200 hover:bg-red-50 hover:border-red-400 hover:text-red-600 font-medium"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Customer</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete {customer.name}? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {isDeleting ? 'Deleting...' : 'Delete'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardFooter>
      </Card>

      {/* Customer Invoices Dialog */}
      <CustomerInvoicesDialog
        open={invoicesDialogOpen}
        onOpenChange={setInvoicesDialogOpen}
        customer={customer}
        onCreateInvoice={handleCreateInvoice}
      />
    </>
  )
}