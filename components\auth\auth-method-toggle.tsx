'use client'

import { <PERSON>, Sparkles } from 'lucide-react'

interface AuthMethodToggleProps {
  authMethod: 'password' | 'magic'
  setAuthMethod: (method: 'password' | 'magic') => void
  onMethodChange: () => void
}

export function AuthMethodToggle({ authMethod, setAuthMethod, onMethodChange }: AuthMethodToggleProps) {
  return (
    <fieldset className="mb-6">
      <legend className="sr-only">Choose authentication method</legend>
      <div className="grid grid-cols-2 gap-2 p-1 bg-slate-100 rounded-2xl" role="radiogroup" aria-label="Authentication method">
        <button
          type="button"
          onClick={() => { setAuthMethod('magic'); onMethodChange(); }}
          className={`flex items-center justify-center space-x-2 py-3 px-4 rounded-xl font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            authMethod === 'magic'
              ? 'bg-white shadow-sm text-slate-900'
              : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
          }`}
          role="radio"
          aria-checked={authMethod === 'magic'}
          aria-describedby="magic-link-description"
        >
          <Sparkles className="h-4 w-4" aria-hidden="true" />
          <span>Magic Link</span>
        </button>
        <button
          type="button"
          onClick={() => { setAuthMethod('password'); onMethodChange(); }}
          className={`flex items-center justify-center space-x-2 py-3 px-4 rounded-xl font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            authMethod === 'password'
              ? 'bg-white shadow-sm text-slate-900'
              : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
          }`}
          role="radio"
          aria-checked={authMethod === 'password'}
          aria-describedby="password-description"
        >
          <Lock className="h-4 w-4" aria-hidden="true" />
          <span>Password</span>
        </button>
      </div>
      <div className="sr-only">
        <p id="magic-link-description">Sign in with a secure link sent to your email</p>
        <p id="password-description">Sign in with your email and password</p>
      </div>
    </fieldset>
  )
}
