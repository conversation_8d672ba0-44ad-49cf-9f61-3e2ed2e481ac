'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, UserPlus, TrendingUp, Building2 } from 'lucide-react'

interface CustomersStatsProps {
  totalCustomers: number
  newThisMonth: number
}

export function CustomersStats({ totalCustomers, newThisMonth }: CustomersStatsProps) {
  const stats = [
    {
      title: 'Total Customers',
      value: totalCustomers,
      icon: Users,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      borderColor: 'border-emerald-200',
      gradient: 'success-gradient',
    },
    {
      title: 'New This Month',
      value: newThisMonth,
      icon: UserPlus,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      gradient: 'action-gradient',
    },
    {
      title: 'Active Clients',
      value: totalCustomers, // For now, assume all are active
      icon: TrendingUp,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200',
      gradient: 'warning-gradient',
    },
    {
      title: 'Companies',
      value: Math.floor(totalCustomers * 0.6), // Estimate
      icon: Building2,
      color: 'text-slate-600',
      bgColor: 'bg-slate-50',
      borderColor: 'border-slate-200',
      gradient: 'brand-gradient',
    },
  ]

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index} className="professional-card card-hover border-0 shadow-professional bg-white/90 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-semibold text-slate-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2.5 rounded-xl ${stat.bgColor} ${stat.borderColor} border`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="amount-display text-2xl font-bold text-slate-900 mb-1">
                {stat.value}
              </div>
              <div className={`h-1 w-full rounded-full ${stat.gradient}`}></div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}