'use client'

import { InvoiceCard } from '@/components/dashboard/invoice-card'
import { Invoice } from '@/hooks/use-invoices'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { FileText, Search, Filter } from 'lucide-react'

interface InvoicesGridProps {
  invoices: Invoice[]
  onUpdateStatus: (id: string, status: 'paid' | 'delivered') => void
  searchQuery: string
  statusFilter: string
}

export function InvoicesGrid({ invoices, onUpdateStatus, searchQuery, statusFilter }: InvoicesGridProps) {
  const { language } = useLanguage()

  if (invoices.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-20 h-20 rounded-2xl bg-slate-100 flex items-center justify-center mx-auto mb-6">
          {searchQuery || statusFilter !== 'all' ? (
            <Search className="h-10 w-10 text-slate-400" />
          ) : (
            <FileText className="h-10 w-10 text-slate-400" />
          )}
        </div>
        
        {searchQuery || statusFilter !== 'all' ? (
          <div>
            <h3 className="text-lg font-semibold text-slate-900 mb-2">No invoices found</h3>
            <p className="text-slate-600 font-medium mb-4">
              Try adjusting your search or filter criteria
            </p>
            <div className="flex flex-wrap gap-2 justify-center text-sm text-slate-500">
              {searchQuery && (
                <span className="bg-slate-100 px-3 py-1 rounded-full">
                  Search: "{searchQuery}"
                </span>
              )}
              {statusFilter !== 'all' && (
                <span className="bg-slate-100 px-3 py-1 rounded-full">
                  Status: {getTranslation(language, statusFilter as any)}
                </span>
              )}
            </div>
          </div>
        ) : (
          <div>
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              {getTranslation(language, 'noInvoices')}
            </h3>
            <p className="text-slate-600 font-medium">
              Create your first invoice to get started
            </p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {invoices.map((invoice) => (
        <InvoiceCard
          key={invoice.id}
          invoice={invoice}
          onUpdateStatus={onUpdateStatus}
        />
      ))}
    </div>
  )
}