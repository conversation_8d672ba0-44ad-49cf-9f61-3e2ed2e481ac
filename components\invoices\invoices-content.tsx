'use client'

import { useState } from 'react'
import { useLanguage } from '@/contexts/language-context'
import { getTranslation } from '@/lib/i18n'
import { useInvoices } from '@/hooks/use-invoices'
import { InvoicesHeader } from '@/components/invoices/invoices-header'
import { InvoicesFilters } from '@/components/invoices/invoices-filters'
import { InvoicesGrid } from '@/components/invoices/invoices-grid'
import { InvoicesStats } from '@/components/invoices/invoices-stats'
import { AddInvoiceDialog } from '@/components/dashboard/add-invoice-dialog'
import { BottomNav } from '@/components/navigation/bottom-nav'
import { FabSpeedDial } from '@/components/navigation/fab-speed-dial'

export function InvoicesContent() {
  const { language } = useLanguage()
  const {
    invoices,
    paidInvoices,
    pendingInvoices,
    deliveredInvoices,
    totalPaid,
    totalPending,
    loading,
    updateInvoiceStatus,
    addInvoice,
  } = useInvoices()

  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'paid' | 'pending' | 'delivered'>('all')
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'client'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="text-center">
          <div className="w-16 h-16 rounded-2xl brand-gradient flex items-center justify-center mb-4 mx-auto">
            <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
          </div>
          <p className="text-slate-600 font-medium">Loading invoices...</p>
        </div>
      </div>
    )
  }

  // Filter and sort invoices
  const filteredInvoices = invoices
    .filter(invoice => {
      const matchesSearch = searchQuery === '' || 
        invoice.client_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invoice.service_description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (invoice.invoice_number && invoice.invoice_number.toLowerCase().includes(searchQuery.toLowerCase()))
      
      const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter
      
      return matchesSearch && matchesStatus
    })
    .sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          break
        case 'amount':
          comparison = a.amount - b.amount
          break
        case 'client':
          comparison = a.client_name.localeCompare(b.client_name)
          break
      }
      
      return sortOrder === 'asc' ? comparison : -comparison
    })

  const handleAddCustomer = () => {
    // Placeholder for future customer functionality
    console.log('Add Customer functionality - Coming soon!')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-slate-100/30 to-blue-50/20">
      <InvoicesHeader />
      
      <main className="clean-layout py-6 pb-20">
        {/* Stats Overview */}
        <InvoicesStats
          totalPaid={totalPaid}
          totalPending={totalPending}
          totalInvoices={invoices.length}
          totalDelivered={deliveredInvoices.length}
        />

        {/* Filters and Search */}
        <InvoicesFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          statusFilter={statusFilter}
          onStatusFilterChange={setStatusFilter}
          sortBy={sortBy}
          onSortByChange={setSortBy}
          sortOrder={sortOrder}
          onSortOrderChange={setSortOrder}
          totalCount={filteredInvoices.length}
          onAddInvoice={() => setAddDialogOpen(true)}
        />

        {/* Invoices Grid */}
        <InvoicesGrid
          invoices={filteredInvoices}
          onUpdateStatus={updateInvoiceStatus}
          searchQuery={searchQuery}
          statusFilter={statusFilter}
        />
      </main>

      {/* Bottom Navigation */}
      <BottomNav />
      
      {/* Floating Action Button */}
      <FabSpeedDial 
        onAddInvoice={() => setAddDialogOpen(true)}
        onAddCustomer={handleAddCustomer}
      />
      
      {/* Add Invoice Dialog */}
      <AddInvoiceDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        onAdd={addInvoice}
      />
    </div>
  )
}
