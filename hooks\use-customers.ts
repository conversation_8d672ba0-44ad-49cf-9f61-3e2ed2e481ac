'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { toast } from 'sonner'

export interface Customer {
  id: string
  user_id: string
  name: string
  email: string | null
  phone: string | null
  company: string | null
  address_line_1: string | null
  address_line_2: string | null
  city: string | null
  state: string | null
  postal_code: string | null
  country: string | null
  notes: string | null
  created_at: string
  updated_at: string
}

export function useCustomers() {
  const { user } = useAuth()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)

  const fetchCustomers = async () => {
    if (!user) return

    try {
      const response = await fetch('/api/customers', { credentials: 'include' })
      if (!response.ok) throw new Error('Failed to fetch')
      const { data } = await response.json()
      setCustomers(data || [])
    } catch (error) {
      console.error('Error fetching customers:', error)
      // toast.error('Failed to fetch customers')
    } finally {
      setLoading(false)
    }
  }

  const addCustomer = async (customer: Omit<Customer, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user) return

    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customer),
        credentials: 'include',
      })
      if (!response.ok) {
        const { error } = await response.json()
        throw new Error(error)
      }
      const { data } = await response.json()
      setCustomers(prev => [data, ...prev])
      toast.success('Customer added successfully')
      return data
    } catch (error: any) {
      console.error('Error adding customer:', error)
      if (error.message.includes('23505')) {
        toast.error('A customer with this email already exists')
      } else {
      // toast.error('Failed to add customer')
      }
      throw error
    }
  }

  const updateCustomer = async (id: string, updates: Partial<Omit<Customer, 'id' | 'user_id' | 'created_at' | 'updated_at'>>) => {
    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
        credentials: 'include',
      })
      if (!response.ok) {
        const { error } = await response.json()
        throw new Error(error)
      }
      const { data } = await response.json()
      setCustomers(prev => prev.map(customer =>
        customer.id === id ? data : customer
      ))
      toast.success('Customer updated successfully')
      return data
    } catch (error: any) {
      console.error('Error updating customer:', error)
      if (error.message.includes('23505')) {
        toast.error('A customer with this email already exists')
      } else {
      // toast.error('Failed to update customer')
      }
      throw error
    }
  }

  const deleteCustomer = async (id: string) => {
    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      })
      if (!response.ok) {
        const { error } = await response.json()
        throw new Error(error)
      }
      setCustomers(prev => prev.filter(customer => customer.id !== id))
      toast.success('Customer deleted successfully')
    } catch (error) {
      console.error('Error deleting customer:', error)
      // toast.error('Failed to delete customer')
    }
  }

  useEffect(() => {
    fetchCustomers()
  }, [user])

  return {
    customers,
    loading,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    refreshCustomers: fetchCustomers,
  }
}
