'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { PhoneAuth } from '@/components/auth/phone-auth'
import { Dashboard } from '@/components/dashboard/dashboard'

export default function DashboardPage() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()

  // Redirect if not authenticated after loading
  useEffect(() => {
    if (!authLoading && !user) {
      router.replace('/') // Redirect to login page
    }
  }, [user, authLoading, router])

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    // This case should ideally be handled by the useEffect redirect,
    // but as a fallback, we can render the PhoneAuth component.
    return <PhoneAuth />
  }

  // If authenticated, render the dashboard content
  return <Dashboard />
}
